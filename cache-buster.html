<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cache Buster - PixelArtNexus</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 50px auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    h1 {
      color: #2c5aa0;
      text-align: center;
    }
    .button {
      background-color: #2c5aa0;
      color: white;
      padding: 12px 24px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 16px;
      margin: 10px;
      text-decoration: none;
      display: inline-block;
    }
    .button:hover {
      background-color: #1e3a70;
    }
    .info {
      background-color: #e8f4fd;
      border: 1px solid #b3d9f7;
      border-radius: 6px;
      padding: 15px;
      margin: 20px 0;
    }
    .links {
      text-align: center;
      margin: 30px 0;
    }
    .timestamp {
      font-family: monospace;
      background-color: #f0f0f0;
      padding: 2px 6px;
      border-radius: 3px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔄 Cache Buster Tool</h1>
    
    <div class="info">
      <strong>💡 What this does:</strong><br>
      This tool adds a timestamp parameter to all your page URLs to force the browser to load fresh versions instead of cached ones.
    </div>

    <div class="info">
      <strong>🕒 Current timestamp:</strong> <span class="timestamp" id="timestamp"></span>
    </div>

    <div class="links">
      <h3>🚀 Test Fresh Versions:</h3>
      <a href="/" class="button" id="indexLink">Home Page</a>
      <a href="/canvas" class="button" id="canvasLink">Canvas Editor</a>
      <a href="/gallery/main" class="button" id="galleryLink">Gallery</a>
      <a href="/contact" class="button" id="contactLink">Contact</a>
      <a href="/about" class="button" id="aboutLink">About</a>
      <a href="/login" class="button" id="loginLink">Login</a>
    </div>

    <div class="info">
      <strong>🛠️ Manual Cache Clearing:</strong><br>
      • <strong>Chrome/Edge:</strong> Ctrl+Shift+R or F12 → Network tab → "Disable cache"<br>
      • <strong>Firefox:</strong> Ctrl+Shift+R or F12 → Network tab → Settings gear → "Disable cache"<br>
      • <strong>Safari:</strong> Cmd+Option+R or Develop menu → "Empty Caches"
    </div>

    <div class="info">
      <strong>🔧 For Developers:</strong><br>
      Run <code>npm run dev:no-cache</code> to start the development server with caching disabled.
    </div>
  </div>

  <script>
    // Generate timestamp for cache busting
    const timestamp = Date.now();
    document.getElementById('timestamp').textContent = timestamp;

    // Add timestamp to all links
    const links = document.querySelectorAll('a.button');
    links.forEach(link => {
      const url = new URL(link.href);
      url.searchParams.set('cb', timestamp);
      link.href = url.toString();
    });

    // Auto-refresh timestamp every 5 seconds
    setInterval(() => {
      const newTimestamp = Date.now();
      document.getElementById('timestamp').textContent = newTimestamp;
      
      // Update all links with new timestamp
      links.forEach(link => {
        const url = new URL(link.href);
        url.searchParams.set('cb', newTimestamp);
        link.href = url.toString();
      });
    }, 5000);
  </script>
</body>
</html>
