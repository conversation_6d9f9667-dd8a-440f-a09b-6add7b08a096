const fs = require('fs');
const path = require('path');

// Generate a cache busting version based on current timestamp
const version = Date.now();
console.log(`🔄 Adding cache busting version: ${version}`);

// List of HTML files to process
const htmlFiles = [
  'dist/index.html',
  'dist/canvas.html',
  'dist/about.html',
  'dist/contact.html',
  'dist/account-required.html',
  'dist/login.html',
  'dist/blog.html',
  'dist/social.html',
  'dist/terms.html',
  'dist/privacy.html',
  'dist/community-guidelines.html',
  'dist/gallery/main.html',
  'dist/gallery/index.html'
];

// Function to add cache busting to a file
function addCacheBusting(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Add cache busting to CSS files
    const cssRegex = /(href=["']?)([^"']*\.css)(\?v=\d+)?(["'])/g;
    content = content.replace(cssRegex, (match, prefix, cssPath, oldVersion, suffix) => {
      modified = true;
      return `${prefix}${cssPath}?v=${version}${suffix}`;
    });

    // Add cache busting to JS files
    const jsRegex = /(src=["']?)([^"']*\.js)(\?v=\d+)?(["'])/g;
    content = content.replace(jsRegex, (match, prefix, jsPath, oldVersion, suffix) => {
      // Skip external URLs (Auth0, Google, etc.)
      if (jsPath.includes('://') || jsPath.startsWith('//')) {
        return match;
      }
      modified = true;
      return `${prefix}${jsPath}?v=${version}${suffix}`;
    });

    // Add cache busting to image files (optional - for critical images)
    const imgRegex = /(src=["']?)([^"']*\.(png|jpg|jpeg|webp|svg))(\?v=\d+)?(["'])/g;
    content = content.replace(imgRegex, (match, prefix, imgPath, ext, oldVersion, suffix) => {
      // Only add cache busting to local images, skip external ones
      if (imgPath.includes('://') || imgPath.startsWith('//')) {
        return match;
      }
      // Only add to critical images like logos and banners
      if (imgPath.includes('title.webp') || imgPath.includes('banner.png') || imgPath.includes('logo.png')) {
        modified = true;
        return `${prefix}${imgPath}?v=${version}${suffix}`;
      }
      return match;
    });

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Updated: ${filePath}`);
    } else {
      console.log(`📄 No changes needed: ${filePath}`);
    }

  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
}

// Process all HTML files
console.log('🚀 Starting cache busting process...\n');

htmlFiles.forEach(filePath => {
  addCacheBusting(filePath);
});

console.log(`\n✨ Cache busting complete! Version: ${version}`);
console.log('💡 All CSS, JS, and critical image files now have cache busting parameters.');
