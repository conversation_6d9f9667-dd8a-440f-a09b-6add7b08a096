<!DOCTYPE html>
<!--[if lt IE 7]>      <html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>         <html class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>         <html class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js">
    <!--<![endif]-->
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <title>rp Pixel Art Gallery</title>
        <meta name="description" content="A gallery of pixel art made with Pixel Art Maker.">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <!-- Place favicon.ico and apple-touch-icon.png in the root directory -->
        <link rel="stylesheet" href="/css/normalize.css">
        <link rel="stylesheet" href="/css/main.css">
        <link rel="stylesheet" href="/css/editor.css">
        <script src="/js/vendor/modernizr-2.6.2.min.js"></script>
    </head>
    <body style="background: #f1f1f1;">
        <!--[if lt IE 7]>
            <p class="browsehappy">You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade your browser</a> to improve your experience.</p>
        <![endif]-->
        <div style="background: #737373;color:white;  padding: 0.2rem 0.4rem;">
            Pinned Channels:
				
            <div id="pinnedChannelsEl" style="display: inline;">
                <div id="pinnedChannelsWrapper" style="display: inline;"></div>
                <span style="opacity:0.6">·</span>
                <span onclick="pinnedChannelsEl.style.display='none'; pinnedChannelsEditor.style.display='inline-block'; pinnedChannelsInput.value=JSON.parse(localStorage.pinnedChannelsData2).pinnedChannels.join(',');" style="font-weight:bold;cursor: pointer;color: #00e6ff;">edit</span>
            </div>
            <div id="pinnedChannelsEditor" style="display: none;">
                <input placeholder="comma,separated,names,..." id="pinnedChannelsInput" autocomplete="off" autocapitalize="off" style="width: 280px;">
                <button onclick="pinnedChannelsEl.style.display='inline-block'; pinnedChannelsEditor.style.display='none'; updatePinnedChannels(pinnedChannelsInput.value.split(','));">save</button>
            </div>
            <div style="cursor:pointer; display:inline-block; float:right;" onclick="window.open(`https://www.google.com/search?q=${prompt('Search for pixel art with these keywords in their title:').replace(/\s+/g, '+')}+pixel+art+site:pixelartmaker.com\u0026source=lnms\u0026tbm=isch\u0026sa=X`)">🔍</div>
            <div style="cursor:pointer;display:inline-block;float:right;margin-right: 0.6rem;" onclick="localStorage.blockedUserIds = JSON.stringify(prompt('List of user IDs to block (separated by commas):', JSON.parse(localStorage.blockedUserIds || '[]').join(',')).split(/[\s,]+/g).map(u => u.toLowerCase())); location.href=location.href;">🚫</div>
            <div id="darkModeBtn" style="cursor:pointer;display:inline-block;float:right;margin-right: 0.6rem;" onclick="localStorage.darkModeEnabled=(localStorage.darkModeEnabled ? '' : '1'); location.href=location.href;">🌃</div>
        </div>
        <script>
            let pinnedChannelsData;
            try {
                pinnedChannelsData = JSON.parse(localStorage.pinnedChannelsData2);
            } catch (e) {
                console.warn("Couldn't load pinned channels (expected for first-time users). Creating fresh pinned channels.");
            }
            if (!pinnedChannelsData) {
                localStorage.pinnedChannelsData2 = JSON.stringify({
                    channelData: {
                        main: {
                            postCountAtLastVisit: 0
                        },
                        chat: {
                            postCountAtLastVisit: 0
                        },
                        rp: {
                            postCountAtLastVisit: 0
                        },
                        cute: {
                            postCountAtLastVisit: 0
                        },
                        random: {
                            postCountAtLastVisit: 0
                        },
                        lgbt: {
                            postCountAtLastVisit: 0
                        },
                    },
                    pinnedChannels: ["main", "chat", "rp", "cute", "random", "lgbt"],
                });
                pinnedChannelsData = JSON.parse(localStorage.pinnedChannelsData2);
            }

            // update this channel's `postCountAtLastVisit` value:
            let thisChannelName = "rp";
            let thisChannelPostCount = Number(2076561);
            if (!pinnedChannelsData.channelData[thisChannelName])
                pinnedChannelsData.channelData[thisChannelName] = {
                    postCountAtLastVisit: 0
                };
            pinnedChannelsData.channelData[thisChannelName].postCountAtLastVisit = thisChannelPostCount;
            localStorage.pinnedChannelsData2 = JSON.stringify(pinnedChannelsData);

            async function updateChannelPostCounts() {
                let counts = await fetch(`/php/getNumPostsOnChannels.php?channel_names=${pinnedChannelsData.pinnedChannels.join(",")}&cacheBust=${Math.random()}`).then(r => r.json());

                // grab a fresh copy of localStorage.pinnedChannelsData because it may have been updated in another tab:
                pinnedChannelsData = JSON.parse(localStorage.pinnedChannelsData2);

                pinnedChannelsWrapper.innerHTML = pinnedChannelsData.pinnedChannels.map(c => {
                    let numNewPosts = counts[c] - pinnedChannelsData.channelData[c].postCountAtLastVisit;
                    let numberIcon = "";
                    if (numNewPosts > 100) {
                        numberIcon = `<span style="font-size:75%;background: #ef0000;border-radius: 10px;padding: 0.15rem;color: white;">99+</span>`;
                    } else if (numNewPosts > 0) {
                        numberIcon = `<span style="font-size:75%;background: #ef0000;border-radius: 10px;padding: 0.15rem;color: white;">${numNewPosts}</span>`;
                    }
                    return `<a href="/gallery${c == "main" ? "" : "/" + c}" style="color:white;">${c}</a> ${numberIcon}`;
                }
                ).join(` <span style="opacity:0.6">·</span> `);
            }
            updateChannelPostCounts();
            setInterval(updateChannelPostCounts, 5000);

            function updatePinnedChannels(channelNames) {
                channelNames = channelNames.map(c => c.trim().toLowerCase());
                if (channelNames.length > 7) {
                    channelNames = channelNames.slice(0, 10);
                    alert("You can currently pin a maximum of 10 channels.");
                }
                pinnedChannelsData = JSON.parse(localStorage.pinnedChannelsData2);
                for (let channelName of channelNames) {
                    if (!pinnedChannelsData.channelData[channelName])
                        pinnedChannelsData.channelData[channelName] = {
                            postCountAtLastVisit: 0
                        };
                }
                pinnedChannelsData.pinnedChannels = channelNames;
                localStorage.pinnedChannelsData2 = JSON.stringify(pinnedChannelsData);
                updateChannelPostCounts();
            }
        </script>
        <!-- <p style="background: #ffd34e;text-align:center">(there was a bug with the blocking feature - should be working now! try clicking the 🚫 button)</p> -->
        <div id="spammerBlockMessage" style="display:none; margin:0 auto;padding:1rem;background: #ffdb77;border-radius: 3px;max-width: 400px;margin-top: 2rem;">
            Note: You can block spammers and trolls using the 🚫 button in the header bar (top-right of this page). Just copy and paste their user ID. <button onclick="spammerBlockMessage.remove(); localStorage.theyKnowAboutBlockingFeature='yes';">okay</button>
        </div>
        <script>
            if (!localStorage.theyKnowAboutBlockingFeature) {
                spammerBlockMessage.style.display = "";
            }
        </script>
        <h1>rp gallery</h1>
        <h3>
            Newest pixel art on the <span style='background: #ffd455;padding: 0.3rem;border-radius: 3px; color: black;'>rp</span>
            channel:
        </h3>
        <div id="gallery" style="display:flex; flex-wrap: wrap; padding: 5px; justify-content: center;">
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/184e911cc60f6d2'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/184e911cc60f6d2.png' alt="[3f58ff] Eyes and the chest..."/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    Eyes and the chest...
                </div>
            </div>
            <div data-user-id='ee3d6a' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/c2ff5e7736d2651'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/c2ff5e7736d2651.png' alt="[ee3d6a] me likes stray"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#ee3d6a;color:#FFFFFF; border: 3px solid #130468; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>ee3d6a</span>
                    me likes stray
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/22f0e20ba3c31a6'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/22f0e20ba3c31a6.png' alt="[3f58ff] Reminds me of, &quot;House MD - Hosue [SIC]&quot;....at the end at least.."/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    Reminds me of, &quot;House MD - Hosue [SIC]&quot;....at the end at least..
                </div>
            </div>
            <div data-user-id='da775c' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="Dell" rel='nofollow' href='//pixelartmaker.com/art/458eb06cbf54728'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/458eb06cbf54728.png' alt="[da775c] ooohhh i'm so good i'm soo good with it i'm good as fuuuck like daaaaaaaa[GE"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#da775c;color:#000000; border: 3px solid #1cb3b3; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>da775c</span>
                    ooohhh i'm so good i'm soo good with it i'm good as fuuuck like daaaaaaaa[GE
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/d6081670f1071c9'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/d6081670f1071c9.png' alt="[3f58ff] *Also a bit more on the torso*"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    *Also a bit more on the torso*
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/7cba5ae0ad38850'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/7cba5ae0ad38850.png' alt="[3f58ff] I got work on the neck, arm, and the hip bone thing"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    I got work on the neck, arm, and the hip bone thing
                </div>
            </div>
            <div data-user-id='da775c' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="Dell" rel='nofollow' href='//pixelartmaker.com/art/43c22ceca87fb10'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/43c22ceca87fb10.png' alt="[da775c] accquired"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#da775c;color:#000000; border: 3px solid #1cb3b3; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>da775c</span>
                    accquired
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/709a281fae1fd15'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/709a281fae1fd15.png' alt="[3f58ff] Delinquent (TEEN TITAN)"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    Delinquent (TEEN TITAN)
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/db02e34b907db1d'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/db02e34b907db1d.png' alt="[3f58ff] Yeah I expected that"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    Yeah I expected that
                </div>
            </div>
            <div data-user-id='ee3d6a' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/da5116c35ffbe8b'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/da5116c35ffbe8b.png' alt="[ee3d6a] also the stray isn't a mechanical object, so that arm can be a lot more bloody"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#ee3d6a;color:#FFFFFF; border: 3px solid #130468; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>ee3d6a</span>
                    also the stray isn't a mechanical object, so that arm can be a lot more bloody
                </div>
            </div>
            <div data-user-id='ee3d6a' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/b836328eb522445'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/b836328eb522445.png' alt="[ee3d6a] if you want, mostly if you rp as strays and Filth its just melee for Filth and ranged throwables like this one for stray"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#ee3d6a;color:#FFFFFF; border: 3px solid #130468; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>ee3d6a</span>
                    if you want, mostly if you rp as strays and Filth its just melee for Filth and ranged throwables like this one for stray
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/ef0ada36008d672'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/ef0ada36008d672.png' alt="[3f58ff] Fine, but I don't know anything about Ultrakill so I'll probably look everything up just to stay on point."/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    Fine, but I don't know anything about Ultrakill so I'll probably look everything up just to stay on point.
                </div>
            </div>
            <div data-user-id='ee3d6a' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/4134a1b5de8e0d5'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/4134a1b5de8e0d5.png' alt="[ee3d6a] bro trust, you gotta join up. nothing stopping you."/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#ee3d6a;color:#FFFFFF; border: 3px solid #130468; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>ee3d6a</span>
                    bro trust, you gotta join up. nothing stopping you.
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/3138e81b466a966'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/3138e81b466a966.png' alt="[3f58ff] Dispite my fictional creature drawings. "/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    Dispite my fictional creature drawings. 
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/219b2af4a2cefd4'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/219b2af4a2cefd4.png' alt="[3f58ff] I haven't RP'ed in like actual years, and plus my kind of RP is more on the realistic side."/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    I haven't RP'ed in like actual years, and plus my kind of RP is more on the realistic side.
                </div>
            </div>
            <div data-user-id='ee3d6a' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/190016846351830'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/190016846351830.png' alt="[ee3d6a] wait why arent you participating"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#ee3d6a;color:#FFFFFF; border: 3px solid #130468; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>ee3d6a</span>
                    wait why arent you participating
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/ba5b061db18da2d'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/ba5b061db18da2d.png' alt="[3f58ff] Also Idk why I'm still doing this. I just like drawing monsters and horror stuff :p"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    Also Idk why I'm still doing this. I just like drawing monsters and horror stuff :p
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/5e6cc2290224e4d'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/5e6cc2290224e4d.png' alt="[3f58ff] ...siht ekil gnihtemos"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    ...siht ekil gnihtemos
                </div>
            </div>
            <div data-user-id='ee3d6a' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/1e3e6aef32ae6c5'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/1e3e6aef32ae6c5.png' alt="[ee3d6a] btw this is the projectile it fires."/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#ee3d6a;color:#FFFFFF; border: 3px solid #130468; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>ee3d6a</span>
                    btw this is the projectile it fires.
                </div>
            </div>
            <div data-user-id='596cb8' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/f0c696427bf6d46'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/f0c696427bf6d46.png' alt="[596cb8] I am so goated"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#596cb8;color:#FFFFFF; border: 3px solid #bb20cf; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>596cb8</span>
                    I am so goated
                </div>
            </div>
            <div data-user-id='596cb8' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/fc316f906a2ced0'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/fc316f906a2ced0.png' alt="[596cb8] dog"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#596cb8;color:#FFFFFF; border: 3px solid #bb20cf; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>596cb8</span>
                    dog
                </div>
            </div>
            <div data-user-id='ee3d6a' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/0a7417f9406395b'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/0a7417f9406395b.png' alt="[ee3d6a] why"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#ee3d6a;color:#FFFFFF; border: 3px solid #130468; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>ee3d6a</span>
                    why
                </div>
            </div>
            <div data-user-id='ee3d6a' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/149d88069589b87'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/149d88069589b87.png' alt="[ee3d6a] boom"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#ee3d6a;color:#FFFFFF; border: 3px solid #130468; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>ee3d6a</span>
                    boom
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/b01065dc212971d'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/b01065dc212971d.png' alt="[3f58ff] I'm still doing this even though I won't participate in the RP lol"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    I'm still doing this even though I won't participate in the RP lol
                </div>
            </div>
            <div data-user-id='ee3d6a' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/50c4d141273ccef'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/50c4d141273ccef.png' alt="[ee3d6a] fuck yes"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#ee3d6a;color:#FFFFFF; border: 3px solid #130468; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>ee3d6a</span>
                    fuck yes
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/8612e8e24e00275'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/8612e8e24e00275.png' alt="[3f58ff] Something like this. "/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    Something like this. 
                </div>
            </div>
            <div data-user-id='da775c' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="Dell" rel='nofollow' href='//pixelartmaker.com/art/ef38dcc4e1523c2'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/ef38dcc4e1523c2.png' alt="[da775c] hrm."/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#da775c;color:#000000; border: 3px solid #1cb3b3; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>da775c</span>
                    hrm.
                </div>
            </div>
            <div data-user-id='ee3d6a' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/7d2bbde0bc839e6'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/7d2bbde0bc839e6.png' alt="[ee3d6a] okay this looks sick"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#ee3d6a;color:#FFFFFF; border: 3px solid #130468; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>ee3d6a</span>
                    okay this looks sick
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/816ff1efefd1631'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/816ff1efefd1631.png' alt="[3f58ff] Notice his ab was messed up a bit. But anyways, Imma do his torso since that's what I'm more familiar with."/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    Notice his ab was messed up a bit. But anyways, Imma do his torso since that's what I'm more familiar with.
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/bb55c6d2345f855'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/bb55c6d2345f855.png' alt="[3f58ff] Got this so far, I'mma remove his left arm for the request"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    Got this so far, I'mma remove his left arm for the request
                </div>
            </div>
            <div data-user-id='ee3d6a' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/79a29c979d3dfba'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/79a29c979d3dfba.png' alt="[ee3d6a] ok. I like it so far"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#ee3d6a;color:#FFFFFF; border: 3px solid #130468; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>ee3d6a</span>
                    ok. I like it so far
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/a10ab44805c9a3e'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/a10ab44805c9a3e.png' alt="[3f58ff] That's my reference for it "/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    That's my reference for it 
                </div>
            </div>
            <div data-user-id='ee3d6a' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/700a67ac28d0776'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/700a67ac28d0776.png' alt="[ee3d6a] if you want to be the Filth and strays during the act 2, all you need to know is that the Filth are melee creatures who run and bite and lunge, and Stray are husks who use their left arm to shoot projectiles. I am making a projectile sprite right now"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#ee3d6a;color:#FFFFFF; border: 3px solid #130468; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>ee3d6a</span>
                    if you want to be the Filth and strays during the act 2, all you need to know is that the Filth are melee creatures who run and bite and lunge, and Stray are husks who use their left arm to shoot projectiles. I am making a projectile sprite right now
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/5dbfcb75d286baa'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/5dbfcb75d286baa.png' alt="[3f58ff] https://ultrakill.fandom.com/wiki/Stray?file=Stray-Trans.png"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    https://ultrakill.fandom.com/wiki/Stray?file=Stray-Trans.png
                </div>
            </div>
            <div data-user-id='ee3d6a' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/711c6f772fe2d60'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/711c6f772fe2d60.png' alt="[ee3d6a] It is indeed good"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#ee3d6a;color:#FFFFFF; border: 3px solid #130468; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>ee3d6a</span>
                    It is indeed good
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/cf77d926a716795'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/cf77d926a716795.png' alt="[3f58ff] This is like a rough one, so I'll fix it up along the way or so."/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    This is like a rough one, so I'll fix it up along the way or so.
                </div>
            </div>
            <div data-user-id='3f58ff' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="wdye" rel='nofollow' href='//pixelartmaker.com/art/83cb5caa534178d'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/83cb5caa534178d.png' alt="[3f58ff] What I got so far for the Stray. I just liked this enemy better over the Filth."/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#3f58ff;color:#FFFFFF; border: 3px solid #2ad729; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>3f58ff</span>
                    What I got so far for the Stray. I just liked this enemy better over the Filth.
                </div>
            </div>
            <div data-user-id='6c4e5b' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/e3854d55bdf5b8b'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/e3854d55bdf5b8b.png' alt="[6c4e5b] uhm... who is the red one?"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#6c4e5b;color:#FFFFFF; border: 3px solid #20d576; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>6c4e5b</span>
                    uhm... who is the red one?
                </div>
            </div>
            <div data-user-id='ee3d6a' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/041b1a85f9135e2'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/041b1a85f9135e2.png' alt="[ee3d6a] Maurice fire the instant kill attack."/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#ee3d6a;color:#FFFFFF; border: 3px solid #130468; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>ee3d6a</span>
                    Maurice fire the instant kill attack.
                </div>
            </div>
            <div data-user-id='6c4e5b' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title="" rel='nofollow' href='//pixelartmaker.com/art/2664eb98123063d'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/2664eb98123063d.png' alt="[6c4e5b] hey chill out, no need to eat skin..."/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#6c4e5b;color:#FFFFFF; border: 3px solid #20d576; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>6c4e5b</span>
                    hey chill out, no need to eat skin...
                </div>
            </div>
            <div data-user-id='6c4e5b' class='art'>
                <a style="margin:auto; width:100%; text-align: center;" title=" " rel='nofollow' href='//pixelartmaker.com/art/1a7cfecf17f8a7e'>
                    <img loading='lazy' src='//pixelartmaker-data-78746291193.nyc3.digitaloceanspaces.com/image/1a7cfecf17f8a7e.png' alt="[6c4e5b] ANY SKIN I CAN EAT?"/>
                </a>
                <div class="titleTextCtn" style="margin:auto;   padding: 10px; box-sizing: border-box; word-break: break-word;">
                    <span style='background:#6c4e5b;color:#FFFFFF; border: 3px solid #20d576; border-radius: 2px;padding: 0.1rem; font-size: 75%; vertical-align: text-top;'>6c4e5b</span>
                    ANY SKIN I CAN EAT?
                </div>
            </div>
        </div>
        <script>
            let blockedUserIds = JSON.parse(localStorage.blockedUserIds || "[]");
            [...gallery.querySelectorAll(".art")].forEach(el => {
                if (blockedUserIds.includes(el.dataset.userId)) {
                    el.remove();
                }
            }
            );
        </script>
        <script>
            if (localStorage.darkModeEnabled) {
                document.body.style.background = "#2f2f2f";
                document.body.style.color = "#eee";
                document.querySelectorAll(".art").forEach(el => el.style.background = "#3e3e3e");
                darkModeBtn.textContent = "🌄";
            }
        </script>
        <br/>
        <br/>
        <br/>
        <br/>
        <div class="nav_ctn">
            <a style="font-size:240%" href="//pixelartmaker.com/gallery/rp?after=5637372">MORE >></a>
        </div>
        <br/>
        <br/>
        <br/>
        <p style="text-align: center; padding: 1rem;">
            You can leave feedback about PAM <a href="https://s.surveyplanet.com/5PiIpwPxJ">here</a>
            , but please note that I don't have a lot of time to work on PAM at the moment. There are lots of other tools out there (see <a href="https://www.google.com/search?q=pixel+art+making+tool+online">here</a>
            for examples) in case you need a tool with more features. Thank you for your understanding!
        </p>
        <br/>
        <style>
            body * {
                font-family: courier;
            }

            h1, h3 {
                text-align: center;
            }

            #gallery .art {
                /*display:inline-block;
				margin-bottom: 10px;
				width:300px;
				height:300px;*/
                width: 300px;
                min-height: 300px;
                max-height: 500px;
                display: flex;
                /*align-items: center;
				justify-content: center;*/
                flex-direction: column;
                background: white;
                margin: 5px;
                overflow-y: auto;
            }

            .art img {
                max-width: 100%;
                max-height: 300px;
                border: solid 5px transparent;
                box-sizing: border-box;
            }

            .art img:hover {
                border: solid 5px #555555;
            }

            .nav_ctn {
                text-align: center;
            }
        </style>
        <!--<script src="//ajax.googleapis.com/ajax/libs/jquery/1.10.2/jquery.min.js"></script>-->
        <script>
            window.jQuery || document.write('<script src="/js/vendor/jquery-1.10.2.min.js"><\/script>')
        </script>
        <script src="/js/plugins.js"></script>
        <!-- <script src="/js/masonry.pkgd.min.js"></script> -->
        <!-- <script src="/js/imagesloaded.js"></script> -->
        <script>
            // var $container = $('#gallery');
            // // initialize
            // $('#gallery').imagesLoaded( function(){
            // 	$container.masonry({
            // 	  columnWidth: 300,
            // 	  "gutter": 10,
            // 	  itemSelector: '.art'
            // 	});
            // });
        </script>
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-KMZZDN61BE"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', 'G-KMZZDN61BE');
        </script>
    </body>
</html>
