<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Account Required | PixelArtNexus</title>
  <link rel="icon" href="/logo.png" type="image/png">
  <link rel="stylesheet" href="shared-layout.css">
  <script src="https://cdn.auth0.com/js/auth0-spa-js/2.0/auth0-spa-js.production.js"></script>
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
       crossorigin="anonymous"></script>
  <style>
    .account-required-container {
      text-align: center;
      padding: 60px 20px;
      max-width: 600px;
      margin: 0 auto;
    }

    .account-required-icon {
      font-size: 5em;
      margin-bottom: 30px;
      opacity: 0.7;
    }

    .account-required-container h1 {
      color: #2c5aa0;
      margin-bottom: 20px;
      font-size: 2.2em;
    }

    .account-required-container p {
      color: #666;
      font-size: 1.1em;
      line-height: 1.6;
      margin-bottom: 30px;
    }

    .benefits-list {
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 30px;
      margin: 30px 0;
      text-align: left;
      border-left: 4px solid #2c5aa0;
    }

    .benefits-list h3 {
      color: #2c5aa0;
      margin-bottom: 20px;
      text-align: center;
    }

    .benefits-list ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .benefits-list li {
      padding: 10px 0;
      border-bottom: 1px solid #e0e0e0;
      position: relative;
      padding-left: 30px;
    }

    .benefits-list li:last-child {
      border-bottom: none;
    }

    .benefits-list li:before {
      content: "✓";
      color: #2c5aa0;
      font-weight: bold;
      position: absolute;
      left: 0;
      top: 10px;
    }

    .action-buttons {
      display: flex;
      gap: 20px;
      justify-content: center;
      flex-wrap: wrap;
      margin-top: 40px;
    }

    .btn {
      padding: 15px 30px;
      border: none;
      border-radius: 6px;
      font-size: 1.1em;
      font-weight: bold;
      text-decoration: none;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-block;
      min-width: 150px;
      text-align: center;
    }

    .btn-primary {
      background-color: #2c5aa0;
      color: white;
    }

    .btn-primary:hover {
      background-color: #1e3a70;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(44, 90, 160, 0.3);
    }

    .btn-secondary {
      background-color: #f5f5f5;
      color: #333;
      border: 2px solid #ddd;
    }

    .btn-secondary:hover {
      background-color: #e0e0e0;
      border-color: #ccc;
      transform: translateY(-2px);
    }

    .security-note {
      background-color: #e8f4fd;
      border: 1px solid #b3d9f7;
      border-radius: 6px;
      padding: 20px;
      margin: 30px 0;
      font-size: 0.95em;
      color: #2c5aa0;
    }

    .security-note strong {
      display: block;
      margin-bottom: 10px;
    }

    /* Dark theme support */
    body.dark-theme .benefits-list {
      background-color: #2a2a2a;
      color: #e0e0e0;
    }

    body.dark-theme .benefits-list li {
      border-bottom-color: #444;
    }

    body.dark-theme .btn-secondary {
      background-color: #3a3a3a;
      color: #e0e0e0;
      border-color: #555;
    }

    body.dark-theme .btn-secondary:hover {
      background-color: #4a4a4a;
      border-color: #666;
    }

    body.dark-theme .security-note {
      background-color: #1a2332;
      border-color: #2c5aa0;
      color: #4a90e2;
    }

    body.dark-theme .account-required-container p {
      color: #ccc;
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .account-required-container {
        padding: 40px 15px;
      }

      .account-required-container h1 {
        font-size: 1.8em;
      }

      .action-buttons {
        flex-direction: column;
        align-items: center;
      }

      .btn {
        width: 100%;
        max-width: 250px;
      }

      .benefits-list {
        padding: 20px;
        margin: 20px 0;
      }
    }
  </style>
</head>
<body>
  <div id="mobileHeader" style="display: none;">
    <!-- This will be shown on mobile only via JavaScript -->
    <div class="title-container">
      <a href="/">
        <img src="title.webp" alt="PixelArtNexus" class="title-image">
      </a>
    </div>
    <!-- The login/settings buttons will be positioned here on mobile via JavaScript -->
  </div>
  
  <div id="fullWidthWrapper">
    <div id="leftBanner" class="banner-div">
      <div class="left-toolbar">
        <div class="title-container">
          <img src="title.webp" alt="PixelArtNexus" class="title-image">
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- leftbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-****************"
             data-ad-slot="**********"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>

    <div id="mainContainer">
      <div class="header">
        <img src="title.webp" alt="PixelArtNexus" class="logo">
      </div>

      <div class="account-required-container">
        <div class="account-required-icon">🔐</div>
        <h1>Account Required</h1>
        <p>
          To access the PixelArt Gallery and share your amazing creations with our community,
          you'll need to create a free account with a verified email address or log in to your existing one.
        </p>

        <div class="benefits-list">
          <h3>Why Create an Account?</h3>
          <ul>
            <li>Browse and discover amazing pixel art from talented artists</li>
            <li>Share your own pixel art creations with the community</li>
            <li>Like, comment, and interact with other artists</li>
            <li>Save your favorite artworks to your personal collection</li>
            <li>Follow your favorite artists and get notified of new uploads</li>
            <li>Sync your settings and preferences across all devices</li>
            <li>Access cloud storage for your pixel art projects</li>
            <li>Participate in community challenges and events</li>
          </ul>
        </div>

        <div class="security-note">
          <strong>🛡️ Your Privacy & Security</strong>
          We use Auth0 for secure authentication. Your personal information is protected,
          and we only collect what's necessary to provide you with the best experience.
          <br><br>
          <strong>📧 Email Verification Required</strong>
          To ensure community safety and prevent spam, you'll need to verify your email address
          before accessing the gallery features.
        </div>

        <div class="action-buttons">
          <a href="/login" class="btn btn-primary">Create Account / Login</a>
          <a href="/" class="btn btn-secondary">Back to Home</a>
        </div>
      </div>
    </div>

    <div id="rightBanner" class="banner-div">
      <div class="right-toolbar">
        <button id="authButton" style="display: none;">Login</button>
        <button id="settingsButton">Settings</button>
        
        <!-- Profile Dropdown (hidden by default) -->
        <div class="profile-dropdown" id="profileDropdown" style="display: none;">
          <div class="profile-avatar" id="profileAvatar">
            <img id="profileImage" src="" alt="Profile" class="profile-image">
          </div>
          <div class="profile-menu" id="profileMenu">
            <div class="profile-menu-item" id="userSettingsMenuItem">
              <span class="profile-menu-icon">⚙️</span>
              User Settings
            </div>
            <div class="profile-menu-item" id="savedArtMenuItem">
              <span class="profile-menu-icon">💾</span>
              Saved Art
            </div>
            <div class="profile-menu-item" id="logoutMenuItem">
              <span class="profile-menu-icon">🚪</span>
              Logout
            </div>
          </div>
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- rightbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-****************"
             data-ad-slot="4302049929"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>
  </div>

  <!-- Settings Modal -->
  <div id="settingsModal" class="modal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Settings</h2>
        <button class="modal-close-btn">&times;</button>
      </div>
      <div class="modal-body">
        <div class="setting-group">
          <label for="themeSelect">Theme:</label>
          <select id="themeSelect">
            <option value="light">Light</option>
            <option value="dark">Dark</option>
          </select>
        </div>
        
        <div id="userProfileSection" style="display: none;">
          <div class="setting-group">
            <label for="usernameInput">Username:</label>
            <input type="text" id="usernameInput" placeholder="Enter your username" maxlength="50">
          </div>
          
          <div class="setting-group">
            <label for="profileImageInput">Profile Image:</label>
            <input type="file" id="profileImageInput" accept="image/png,image/jpeg,image/jpg">
            <img id="profileImagePreview" style="display: none; max-width: 100px; max-height: 100px; margin-top: 10px; border-radius: 50%;">
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button id="saveSettingsBtn" class="btn-primary">Save</button>
        <button id="cancelSettingsBtn" class="btn-secondary">Cancel</button>
      </div>
    </div>
  </div>

  <script src="shared-layout.js"></script>
</body>
</html>
