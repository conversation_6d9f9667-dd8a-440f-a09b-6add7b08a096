<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gallery | PixelArtNexus</title>
  <link rel="icon" href="/logo.png" type="image/png">
  <link rel="stylesheet" href="/shared-layout.css">
  <script src="https://cdn.auth0.com/js/auth0-spa-js/2.0/auth0-spa-js.production.js"></script>
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
       crossorigin="anonymous"></script>
  <style>
    .gallery-header {
      text-align: center;
      margin-bottom: 20px; /* Reduced from 40px */
      padding-bottom: 15px; /* Reduced from 20px */
      border-bottom: 2px solid #e0e0e0;
    }

    .gallery-header h1 {
      color: #2c5aa0;
      margin-bottom: 8px; /* Reduced from 10px */
      font-size: 1.8em; /* Smaller title */
    }

    .gallery-header p {
      color: #666;
      font-size: 1em; /* Reduced from 1.1em */
      margin: 0;
    }

    .gallery-navigation {
      display: flex;
      justify-content: center;
      gap: 15px; /* Reduced from 20px */
      margin-bottom: 20px; /* Reduced from 30px */
      flex-wrap: wrap;
    }

    .gallery-nav-btn {
      background-color: #2c5aa0;
      color: white;
      padding: 8px 16px; /* Reduced from 12px 24px */
      border: none;
      border-radius: 4px; /* Reduced from 6px */
      text-decoration: none;
      font-weight: bold;
      font-size: 0.9em; /* Smaller text */
      transition: background-color 0.3s ease;
      cursor: pointer;
      display: inline-block;
    }

    .gallery-nav-btn:hover {
      background-color: #1e3a70;
    }

    .gallery-nav-btn.active {
      background-color: #1e3a70;
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .gallery-nav-btn.disabled {
      background-color: #ccc;
      cursor: not-allowed;
      opacity: 0.6;
    }

    .gallery-content {
      min-height: 400px;
      padding: 10px 0; /* Reduced from 20px */
    }

    /* Gallery Posts Grid */
    .gallery-posts-grid {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 8px; /* Further reduced for more compact layout */
      margin-top: 5px; /* Further reduced */
      width: 100%;
      padding: 0 5px; /* Smaller padding */
    }

    .gallery-post {
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      overflow: hidden;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      border: 1px solid #e0e0e0;
      width: 250px; /* Fixed width - never changes */
      height: 350px; /* Fixed height - never changes */
      display: flex;
      flex-direction: column;
      flex-shrink: 0; /* Prevent shrinking */
    }

    .gallery-post:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    }

    .post-image {
      width: 100%;
      height: 200px; /* Keep same image area height */
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f8f8f8;
      flex-shrink: 0; /* Prevent shrinking */
    }

    .placeholder-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain; /* Maintain aspect ratio while fitting */
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 14px;
    }

    /* Different image sizes - now they all fit within the container */
    .placeholder-image.square {
      width: 160px;
      height: 160px;
    }

    .placeholder-image.wide {
      width: 180px;
      height: 90px;
    }

    .placeholder-image.tall {
      width: 80px;
      height: 180px;
    }

    .placeholder-image.thin {
      width: 180px;
      height: 40px;
    }

    /* Placeholder colors */
    .placeholder-image.red { background-color: #e74c3c; }
    .placeholder-image.blue { background-color: #3498db; }
    .placeholder-image.green { background-color: #2ecc71; }
    .placeholder-image.purple { background-color: #9b59b6; }
    .placeholder-image.orange { background-color: #f39c12; }
    .placeholder-image.teal { background-color: #1abc9c; }
    .placeholder-image.pink { background-color: #e91e63; }
    .placeholder-image.yellow { background-color: #f1c40f; }

    .post-content {
      padding: 15px;
      height: 120px; /* Increased from 80px */
      overflow-y: auto;
      flex-shrink: 0; /* Prevent shrinking */
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      gap: 8px;
    }

    .post-username {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 12px;
      font-weight: bold;
      font-size: 12px;
      cursor: pointer;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      margin-bottom: 5px;
      align-self: flex-start;
    }

    .post-username:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    /* Username background styles */
    .username-blue {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .username-blue:hover {
      background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
      animation: gradientShift 2s ease-in-out;
    }

    .username-green {
      background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
      color: white;
    }

    .username-green:hover {
      background: linear-gradient(135deg, #44a08d 0%, #4ecdc4 100%);
      animation: gradientShift 2s ease-in-out;
    }

    .username-purple {
      background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
      color: #333;
    }

    .username-purple:hover {
      background: linear-gradient(135deg, #fed6e3 0%, #a8edea 100%);
      animation: gradientShift 2s ease-in-out;
    }

    .username-orange {
      background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
      color: #333;
    }

    .username-orange:hover {
      background: linear-gradient(135deg, #fecfef 0%, #ff9a9e 100%);
      animation: gradientShift 2s ease-in-out;
    }

    .username-red {
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
      color: white;
    }

    .username-red:hover {
      background: linear-gradient(135deg, #ee5a24 0%, #ff6b6b 100%);
      animation: gradientShift 2s ease-in-out;
    }

    .username-teal {
      background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
      color: white;
    }

    .username-teal:hover {
      background: linear-gradient(135deg, #0984e3 0%, #74b9ff 100%);
      animation: gradientShift 2s ease-in-out;
    }

    .username-gold {
      background: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
      color: #333;
    }

    .username-gold:hover {
      background: linear-gradient(135deg, #ffd200 0%, #f7971e 100%);
      animation: gradientShift 2s ease-in-out;
    }

    .username-dark {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
    }

    .username-dark:hover {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      animation: gradientShift 2s ease-in-out;
    }

    @keyframes gradientShift {
      0%, 100% { transform: translateY(-1px); }
      50% { transform: translateY(-2px) scale(1.02); }
    }

    .post-title {
      color: #333;
      line-height: 1.4;
      word-wrap: break-word;
      font-size: 13px;
      margin-top: 2px;
    }

    .gallery-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .gallery-item {
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 15px;
      text-align: center;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      border: 2px solid transparent;
    }

    .gallery-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      border-color: #2c5aa0;
    }

    .gallery-item img {
      max-width: 100%;
      height: 150px;
      object-fit: contain;
      border-radius: 4px;
      background-color: white;
      border: 1px solid #ddd;
    }

    .gallery-item h3 {
      margin: 10px 0 5px 0;
      color: #2c5aa0;
      font-size: 1em;
    }

    .gallery-item p {
      margin: 0;
      color: #666;
      font-size: 0.9em;
    }

    .coming-soon {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }

    .coming-soon h2 {
      color: #2c5aa0;
      margin-bottom: 15px;
    }

    .coming-soon .icon {
      font-size: 4em;
      margin-bottom: 20px;
      opacity: 0.5;
    }

    .feature-preview {
      background-color: #f0f7ff;
      border: 2px solid #2c5aa0;
      border-radius: 8px;
      padding: 30px;
      margin: 30px 0;
      text-align: center;
    }

    .feature-preview h3 {
      color: #2c5aa0;
      margin-bottom: 15px;
    }

    .feature-list {
      list-style: none;
      padding: 0;
      margin: 20px 0;
    }

    .feature-list li {
      padding: 8px 0;
      color: #555;
    }

    .feature-list li:before {
      content: "🎨 ";
      margin-right: 8px;
    }

    /* Dark theme support */
    body.dark-theme .gallery-item {
      background-color: #2a2a2a;
      color: #e0e0e0;
    }

    body.dark-theme .gallery-item img {
      background-color: #1a1a1a;
      border-color: #444;
    }

    body.dark-theme .feature-preview {
      background-color: #1a2332;
      border-color: #4a90e2;
    }

    body.dark-theme .coming-soon {
      color: #ccc;
    }

    /* Dark theme for gallery posts */
    body.dark-theme .gallery-post {
      background-color: #2a2a2a;
      border-color: #444;
    }

    body.dark-theme .post-image {
      background-color: #1a1a1a;
    }

    body.dark-theme .post-content {
      background-color: #2a2a2a;
    }

    /* Username styles remain the same in dark mode - they have their own gradient colors */

    body.dark-theme .post-title {
      color: #e0e0e0;
    }

    /* Authentication protection */
    .gallery-protected-wrapper {
      display: none; /* Hidden by default until auth check passes */
    }

    .gallery-loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: #ffffff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      color: #333;
    }

    .gallery-loading-overlay .spinner {
      font-size: 3em;
      margin-bottom: 20px;
      animation: spin 1s linear infinite;
    }

    .gallery-loading-overlay h2 {
      margin: 0 0 10px 0;
      font-size: 1.5em;
      color: #2c5aa0;
    }

    .gallery-loading-overlay p {
      margin: 0;
      color: #666;
      font-size: 1.1em;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Dark theme support for loading overlay */
    body.dark-theme .gallery-loading-overlay {
      background-color: #1a1a1a;
      color: #e0e0e0;
    }

    body.dark-theme .gallery-loading-overlay h2 {
      color: #4a90e2;
    }

    body.dark-theme .gallery-loading-overlay p {
      color: #ccc;
    }

    /* Gallery-specific layout adjustments - more zoomed out */
    .gallery-protected-wrapper .banner-div {
      flex: 0 0 150px; /* Thinner banners */
      width: 150px;
      min-width: 150px;
      max-width: 150px;
    }

    .gallery-protected-wrapper #mainContainer {
      flex: 1; /* Take up remaining space */
      width: auto;
      max-width: none;
      min-width: 800px; /* Wider minimum for more content */
      padding: 8px; /* Even smaller padding */
    }

    /* Smaller title image for gallery */
    .gallery-protected-wrapper .title-image {
      max-height: 40px; /* Reduced from default ~60px */
      width: auto;
    }

    /* Smaller logo in header */
    .gallery-protected-wrapper .logo {
      max-height: 50px; /* Reduced from default */
      width: auto;
    }

    /* Responsive design */
    /* Responsive adjustments for smaller screens */
    @media (max-width: 1200px) {
      .gallery-protected-wrapper .banner-div {
        flex: 0 0 120px; /* Even smaller banners */
        width: 120px;
        min-width: 120px;
        max-width: 120px;
      }

      .gallery-protected-wrapper #mainContainer {
        min-width: 600px;
      }
    }

    @media (max-width: 1000px) {
      .gallery-protected-wrapper .banner-div {
        flex: 0 0 100px; /* Very small banners */
        width: 100px;
        min-width: 100px;
        max-width: 100px;
      }

      .gallery-protected-wrapper #mainContainer {
        min-width: 500px;
      }
    }

    @media (max-width: 768px) {
      .gallery-navigation {
        flex-direction: column;
        align-items: center;
      }

      .gallery-nav-btn {
        width: 200px;
        text-align: center;
      }

      .gallery-posts-grid {
        grid-template-columns: 1fr;
        gap: 15px;
      }

      .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
      }

      .gallery-item img {
        height: 120px;
      }

      /* Reset to normal layout on mobile */
      .gallery-protected-wrapper .banner-div {
        flex: 1;
      }

      .gallery-protected-wrapper #mainContainer {
        width: 100%;
        max-width: 100%;
      }
    }
  </style>
</head>
<body>
  <div id="mobileHeader" style="display: none;">
    <!-- This will be shown on mobile only via JavaScript -->
    <div class="title-container">
      <a href="/">
        <img src="/title.webp" alt="PixelArtNexus" class="title-image">
      </a>
    </div>
    <!-- The login/settings buttons will be positioned here on mobile via JavaScript -->
  </div>

  <!-- Full-screen loading overlay -->
  <div class="gallery-loading-overlay" id="galleryLoadingOverlay">
    <div class="spinner">🔄</div>
    <h2>Checking Access...</h2>
    <p>Verifying your account status...</p>
  </div>

  <!-- Protected content wrapper - hidden by default -->
  <div class="gallery-protected-wrapper" id="galleryProtectedWrapper">
    <div id="fullWidthWrapper">
    <div id="leftBanner" class="banner-div">
      <div class="left-toolbar">
        <div class="title-container">
          <img src="/title.webp" alt="PixelArtNexus" class="title-image">
        </div>
      </div>
      <div class="banner-image-container">
        <img src="/banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- leftbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-****************"
             data-ad-slot="**********"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>

    <div id="mainContainer">
      <div class="header">
        <img src="/title.webp" alt="PixelArtNexus" class="logo">
        <div class="gallery-header">
          <h1>PixelArt Gallery</h1>
          <p>Discover amazing pixel art creations from our community</p>
        </div>
      </div>

      <!-- Gallery Navigation -->
      <div class="gallery-navigation">
        <a href="#" class="gallery-nav-btn active" id="featuredBtn">Featured</a>
        <a href="#" class="gallery-nav-btn disabled" id="recentBtn">Recent</a>
        <a href="#" class="gallery-nav-btn disabled" id="popularBtn">Popular</a>
        <a href="#" class="gallery-nav-btn disabled" id="categoriesBtn">Categories</a>
        <a href="#" class="gallery-nav-btn disabled" id="myArtBtn">My Art</a>
      </div>

      <!-- Gallery Content -->
      <div class="gallery-content" id="galleryContent">
        <!-- Gallery Posts Grid -->
        <div class="gallery-posts-grid" id="galleryPostsGrid">
          <!-- Test Post 1 -->
          <div class="gallery-post">
            <div class="post-image">
              <div class="placeholder-image square red"></div>
            </div>
            <div class="post-content">
              <div class="post-username username-blue">PixelMaster99</div>
              <div class="post-title">Epic dragon breathing fire!</div>
            </div>
          </div>

          <!-- Test Post 2 -->
          <div class="gallery-post">
            <div class="post-image">
              <div class="placeholder-image wide blue"></div>
            </div>
            <div class="post-content">
              <div class="post-username username-green">ArtisticSoul</div>
              <div class="post-title">Sunset landscape with mountains and clouds in the background, really peaceful scene</div>
            </div>
          </div>

          <!-- Test Post 3 -->
          <div class="gallery-post">
            <div class="post-image">
              <div class="placeholder-image tall green"></div>
            </div>
            <div class="post-content">
              <div class="post-username username-purple">RetroGamer</div>
              <div class="post-title">8-bit character sprite</div>
            </div>
          </div>

          <!-- Test Post 4 -->
          <div class="gallery-post">
            <div class="post-image">
              <div class="placeholder-image square purple"></div>
            </div>
            <div class="post-content">
              <div class="post-username username-orange">PixelWizard</div>
              <div class="post-title">Magical potion bottle with sparkles and glowing effects that took me hours to perfect</div>
            </div>
          </div>

          <!-- Test Post 5 -->
          <div class="gallery-post">
            <div class="post-image">
              <div class="placeholder-image wide orange"></div>
            </div>
            <div class="post-content">
              <div class="post-username username-red">CreativeNinja</div>
              <div class="post-title">Cyberpunk cityscape at night</div>
            </div>
          </div>

          <!-- Test Post 6 -->
          <div class="gallery-post">
            <div class="post-image">
              <div class="placeholder-image square teal"></div>
            </div>
            <div class="post-content">
              <div class="post-username username-teal">ArtLover42</div>
              <div class="post-title">Cute cat sitting by a window with detailed fur texture and realistic lighting effects</div>
            </div>
          </div>

          <!-- Test Post 7 -->
          <div class="gallery-post">
            <div class="post-image">
              <div class="placeholder-image thin pink"></div>
            </div>
            <div class="post-content">
              <div class="post-username username-gold">PixelPro</div>
              <div class="post-title">Minimalist tree silhouette</div>
            </div>
          </div>

          <!-- Test Post 8 -->
          <div class="gallery-post">
            <div class="post-image">
              <div class="placeholder-image square yellow"></div>
            </div>
            <div class="post-content">
              <div class="post-username username-dark">DigitalArtist</div>
              <div class="post-title">Abstract geometric pattern with intricate details and vibrant colors that represents the harmony between technology and nature in modern digital art</div>
            </div>
          </div>
        </div>

        <a href="/" class="back-link">← Back to Home</a>
      </div>
    </div>

    <div id="rightBanner" class="banner-div">
      <div class="right-toolbar">
        <button id="authButton" style="display: none;">Login</button>
        <button id="settingsButton">Settings</button>
        
        <!-- Profile Dropdown (hidden by default) -->
        <div class="profile-dropdown" id="profileDropdown" style="display: none;">
          <div id="profileAvatar" class="profile-avatar">
            <img id="profileImage" src="" alt="Profile" class="profile-image">
          </div>
          <div id="profileMenu" class="profile-menu" style="display: none;">
            <div class="profile-menu-item" id="userSettingsMenuItem">User Settings</div>
            <div class="profile-menu-item" id="savedArtMenuItem">Saved Art</div>
            <div class="profile-menu-item" id="logoutMenuItem">Logout</div>
          </div>
        </div>
      </div>
      <div class="banner-image-container">
        <img src="/banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- rightbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-****************"
             data-ad-slot="4302049929"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>
  </div>
  </div> <!-- End gallery-protected-wrapper -->

  <!-- Settings Modal -->
  <div id="settingsModal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
      <button type="button" class="modal-close-btn" aria-label="Close">&times;</button>
      <h2>Settings</h2>

      <!-- Theme Settings -->
      <div class="settings-section">
        <h3>Appearance</h3>
        <div>
          <label for="themeSelect">Theme:</label>
          <select id="themeSelect">
            <option value="light">Light</option>
            <option value="dark">Dark</option>
          </select>
        </div>
      </div>

      <!-- User Profile Settings (shown only when authenticated) -->
      <div id="userProfileSection" class="settings-section" style="display: none;">
        <h3>Profile</h3>
        <div>
          <label for="usernameInput">Username:</label>
          <input type="text" id="usernameInput" placeholder="Enter your username" maxlength="50">
        </div>
        <div>
          <label for="profileImageInput">Profile Picture:</label>
          <input type="file" id="profileImageInput" accept="image/png,image/jpg,image/jpeg">
          <img id="profileImagePreview" style="display: none; width: 40px; height: 40px; border-radius: 50%; margin-top: 10px;">
        </div>
      </div>

      <!-- Modal Buttons -->
      <div class="modal-buttons">
        <button type="button" id="cancelSettingsBtn" class="modal-button secondary">Cancel</button>
        <button type="button" id="saveSettingsBtn" class="modal-button primary">Save Settings</button>
      </div>
    </div>
  </div>

  <script src="/shared-layout.js"></script>
  <script>
    // Gallery-specific authentication check
    async function checkGalleryAccess() {
      console.log('🔒 Starting gallery access check...');

      // Wait for Auth0 to initialize with longer timeout
      let attempts = 0;
      const maxAttempts = 100; // 10 seconds max wait

      while (!auth0Client && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }

      // If Auth0 failed to initialize, redirect to account required
      if (!auth0Client) {
        console.log('🚫 Auth0 not available, redirecting to account required page');
        window.location.replace('/account-required');
        return;
      }

      try {
        // Multiple attempts to check authentication
        let authenticated = false;
        let authAttempts = 0;
        const maxAuthAttempts = 3;

        while (authAttempts < maxAuthAttempts) {
          try {
            authenticated = await auth0Client.isAuthenticated();
            console.log(`🔍 Authentication check attempt ${authAttempts + 1}: ${authenticated}`);
            break; // Success, exit retry loop
          } catch (authError) {
            console.warn(`⚠️ Auth check attempt ${authAttempts + 1} failed:`, authError);
            authAttempts++;
            if (authAttempts < maxAuthAttempts) {
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }
        }

        if (!authenticated) {
          console.log('🚫 User not authenticated, redirecting to account required page');
          window.location.replace('/account-required');
          return;
        }

        // Get user data from Auth0
        let user = null;
        let userAttempts = 0;
        const maxUserAttempts = 3;

        while (userAttempts < maxUserAttempts) {
          try {
            user = await auth0Client.getUser();
            console.log('👤 Auth0 user data retrieved:', user ? 'Success' : 'Failed');
            break; // Success, exit retry loop
          } catch (userError) {
            console.warn(`⚠️ Auth0 user data attempt ${userAttempts + 1} failed:`, userError);
            userAttempts++;
            if (userAttempts < maxUserAttempts) {
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }
        }

        if (!user || !user.email) {
          console.log('🚫 No user or email found, redirecting to account required page');
          window.location.replace('/account-required');
          return;
        }

        // Check email verification status from Xano database
        console.log('🔍 Checking email verification status in Xano for:', user.email);
        let xanoUserData = null;
        let xanoAttempts = 0;
        const maxXanoAttempts = 3;

        while (xanoAttempts < maxXanoAttempts) {
          try {
            // Use the same XanoService from shared-layout.js
            if (typeof XanoService !== 'undefined') {
              xanoUserData = await XanoService.getUserSettings(user.email);
              console.log('📊 Xano user data retrieved:', xanoUserData ? 'Success' : 'No data found');
              break;
            } else {
              console.error('❌ XanoService not available');
              break;
            }
          } catch (xanoError) {
            console.warn(`⚠️ Xano data attempt ${xanoAttempts + 1} failed:`, xanoError);
            xanoAttempts++;
            if (xanoAttempts < maxXanoAttempts) {
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }
        }

        // Check email verification from Xano data
        let emailVerified = false;
        if (xanoUserData) {
          // Handle nested response structure from Xano
          const userData = xanoUserData.user_settings || xanoUserData;
          emailVerified = userData.email_verified === true;
          console.log('📧 Xano email verification status:', emailVerified);
          console.log('📊 Full Xano user data:', {
            id: userData.id,
            email: userData.email,
            email_verified: userData.email_verified,
            username: userData.username
          });
        } else {
          console.log('⚠️ No Xano user data found - treating as unverified');
        }

        if (!emailVerified) {
          console.log('🚫 Email not verified in Xano database, redirecting to account required page');
          window.location.replace('/account-required');
          return;
        }

        console.log('✅ User authenticated and email verified in Xano, allowing gallery access');
        console.log('Auth0 user email:', user.email);
        console.log('Xano email verified:', emailVerified);

        // Show the protected content and hide loading overlay
        const loadingOverlay = document.getElementById('galleryLoadingOverlay');
        const protectedWrapper = document.getElementById('galleryProtectedWrapper');

        if (loadingOverlay) {
          loadingOverlay.style.display = 'none';
        }
        if (protectedWrapper) {
          protectedWrapper.style.display = 'block';
        }

      } catch (error) {
        console.error('🚫 Authentication check failed:', error);
        window.location.replace('/account-required');
      }
    }

    // Run authentication check immediately when script loads
    document.addEventListener('DOMContentLoaded', async function() {
      console.log('🚀 Gallery page DOM loaded, starting auth check...');
      await checkGalleryAccess();
    });

    // Also run check after a short delay to catch any late Auth0 initialization
    setTimeout(async () => {
      console.log('🔄 Running delayed gallery auth check...');
      await checkGalleryAccess();
    }, 2000);
  </script>
</body>
</html>
