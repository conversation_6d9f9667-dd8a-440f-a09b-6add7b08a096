<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gallery | PixelArtNexus</title>
  <link rel="icon" href="/logo.png" type="image/png">
  <link rel="stylesheet" href="/shared-layout.css">
  <script src="https://cdn.auth0.com/js/auth0-spa-js/2.0/auth0-spa-js.production.js"></script>
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3586346747453362"
       crossorigin="anonymous"></script>
  <style>
    .gallery-header {
      text-align: center;
      margin-bottom: 40px;
      padding-bottom: 20px;
      border-bottom: 2px solid #e0e0e0;
    }

    .gallery-header h1 {
      color: #2c5aa0;
      margin-bottom: 10px;
    }

    .gallery-header p {
      color: #666;
      font-size: 1.1em;
      margin: 0;
    }

    .gallery-navigation {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-bottom: 30px;
      flex-wrap: wrap;
    }

    .gallery-nav-btn {
      background-color: #2c5aa0;
      color: white;
      padding: 12px 24px;
      border: none;
      border-radius: 6px;
      text-decoration: none;
      font-weight: bold;
      transition: background-color 0.3s ease;
      cursor: pointer;
      display: inline-block;
    }

    .gallery-nav-btn:hover {
      background-color: #1e3a70;
    }

    .gallery-nav-btn.active {
      background-color: #1e3a70;
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .gallery-nav-btn.disabled {
      background-color: #ccc;
      cursor: not-allowed;
      opacity: 0.6;
    }

    .gallery-content {
      min-height: 400px;
      padding: 20px 0;
    }

    .gallery-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .gallery-item {
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 15px;
      text-align: center;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      border: 2px solid transparent;
    }

    .gallery-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      border-color: #2c5aa0;
    }

    .gallery-item img {
      max-width: 100%;
      height: 150px;
      object-fit: contain;
      border-radius: 4px;
      background-color: white;
      border: 1px solid #ddd;
    }

    .gallery-item h3 {
      margin: 10px 0 5px 0;
      color: #2c5aa0;
      font-size: 1em;
    }

    .gallery-item p {
      margin: 0;
      color: #666;
      font-size: 0.9em;
    }

    .coming-soon {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }

    .coming-soon h2 {
      color: #2c5aa0;
      margin-bottom: 15px;
    }

    .coming-soon .icon {
      font-size: 4em;
      margin-bottom: 20px;
      opacity: 0.5;
    }

    .feature-preview {
      background-color: #f0f7ff;
      border: 2px solid #2c5aa0;
      border-radius: 8px;
      padding: 30px;
      margin: 30px 0;
      text-align: center;
    }

    .feature-preview h3 {
      color: #2c5aa0;
      margin-bottom: 15px;
    }

    .feature-list {
      list-style: none;
      padding: 0;
      margin: 20px 0;
    }

    .feature-list li {
      padding: 8px 0;
      color: #555;
    }

    .feature-list li:before {
      content: "🎨 ";
      margin-right: 8px;
    }

    /* Dark theme support */
    body.dark-theme .gallery-item {
      background-color: #2a2a2a;
      color: #e0e0e0;
    }

    body.dark-theme .gallery-item img {
      background-color: #1a1a1a;
      border-color: #444;
    }

    body.dark-theme .feature-preview {
      background-color: #1a2332;
      border-color: #4a90e2;
    }

    body.dark-theme .coming-soon {
      color: #ccc;
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .gallery-navigation {
        flex-direction: column;
        align-items: center;
      }

      .gallery-nav-btn {
        width: 200px;
        text-align: center;
      }

      .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
      }

      .gallery-item img {
        height: 120px;
      }
    }
  </style>
</head>
<body>
  <div id="mobileHeader" style="display: none;">
    <!-- This will be shown on mobile only via JavaScript -->
    <div class="title-container">
      <a href="/">
        <img src="/title.webp" alt="PixelArtNexus" class="title-image">
      </a>
    </div>
    <!-- The login/settings buttons will be positioned here on mobile via JavaScript -->
  </div>

  <div id="fullWidthWrapper">
    <div id="leftBanner" class="banner-div">
      <div class="left-toolbar">
        <div class="title-container">
          <img src="/title.webp" alt="PixelArtNexus" class="title-image">
        </div>
      </div>
      <div class="banner-image-container">
        <img src="/banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- leftbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-3586346747453362"
             data-ad-slot="6832096065"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>

    <div id="mainContainer">
      <div class="header">
        <img src="/title.webp" alt="PixelArtNexus" class="logo">
        <div class="gallery-header">
          <h1>PixelArt Gallery</h1>
          <p>Discover amazing pixel art creations from our community</p>
        </div>
      </div>

      <!-- Gallery Navigation -->
      <div class="gallery-navigation">
        <a href="#" class="gallery-nav-btn active" id="featuredBtn">Featured</a>
        <a href="#" class="gallery-nav-btn disabled" id="recentBtn">Recent</a>
        <a href="#" class="gallery-nav-btn disabled" id="popularBtn">Popular</a>
        <a href="#" class="gallery-nav-btn disabled" id="categoriesBtn">Categories</a>
        <a href="#" class="gallery-nav-btn disabled" id="myArtBtn">My Art</a>
      </div>

      <!-- Gallery Content -->
      <div class="gallery-content" id="galleryContent">
        <!-- Coming Soon Message -->
        <div class="coming-soon">
          <div class="icon">🎨</div>
          <h2>Gallery Coming Soon!</h2>
          <p>We're working hard to bring you an amazing gallery experience where you can browse, share, and discover incredible pixel art creations from our community.</p>
        </div>

        <!-- Feature Preview -->
        <div class="feature-preview">
          <h3>What to Expect</h3>
          <ul class="feature-list">
            <li>Browse featured artwork from talented pixel artists</li>
            <li>Discover recent creations and trending pieces</li>
            <li>Explore art by categories (characters, landscapes, icons, etc.)</li>
            <li>Share your own pixel art creations with the community</li>
            <li>Like, comment, and follow your favorite artists</li>
            <li>Download high-quality versions for inspiration</li>
          </ul>
          <p style="margin-top: 20px; color: #666;">
            <strong>Stay tuned!</strong> The gallery will be launching soon with these exciting features and more.
          </p>
        </div>
      </div>

      <a href="/" class="back-link">← Back to Home</a>
    </div>

    <div id="rightBanner" class="banner-div">
      <div class="right-toolbar">
        <button id="authButton" style="display: none;">Login</button>
        <button id="settingsButton">Settings</button>
        
        <!-- Profile Dropdown (hidden by default) -->
        <div class="profile-dropdown" id="profileDropdown" style="display: none;">
          <div class="profile-avatar" id="profileAvatar">
            <img id="profileImage" src="" alt="Profile" class="profile-image">
          </div>
          <div class="profile-menu" id="profileMenu">
            <div class="profile-menu-item" id="userSettingsMenuItem">
              <span class="profile-menu-icon">⚙️</span>
              User Settings
            </div>
            <div class="profile-menu-item" id="savedArtMenuItem">
              <span class="profile-menu-icon">💾</span>
              Saved Art
            </div>
            <div class="profile-menu-item" id="logoutMenuItem">
              <span class="profile-menu-icon">🚪</span>
              Logout
            </div>
          </div>
        </div>
      </div>
      <div class="banner-image-container">
        <img src="/banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- rightbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-3586346747453362"
             data-ad-slot="4302049929"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>
  </div>

  <!-- Settings Modal -->
  <div id="settingsModal" class="modal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Settings</h2>
        <button class="modal-close-btn">&times;</button>
      </div>
      <div class="modal-body">
        <div class="setting-group">
          <label for="themeSelect">Theme:</label>
          <select id="themeSelect">
            <option value="light">Light</option>
            <option value="dark">Dark</option>
          </select>
        </div>
        
        <div id="userProfileSection" style="display: none;">
          <div class="setting-group">
            <label for="usernameInput">Username:</label>
            <input type="text" id="usernameInput" placeholder="Enter your username" maxlength="50">
          </div>
          
          <div class="setting-group">
            <label for="profileImageInput">Profile Image:</label>
            <input type="file" id="profileImageInput" accept="image/png,image/jpeg,image/jpg">
            <img id="profileImagePreview" style="display: none; max-width: 100px; max-height: 100px; margin-top: 10px; border-radius: 50%;">
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button id="saveSettingsBtn" class="btn-primary">Save</button>
        <button id="cancelSettingsBtn" class="btn-secondary">Cancel</button>
      </div>
    </div>
  </div>

  <script src="/shared-layout.js"></script>
</body>
</html>
