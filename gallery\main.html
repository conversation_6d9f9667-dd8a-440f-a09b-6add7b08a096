<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gallery | PixelArtNexus</title>
  <link rel="icon" href="/logo.png" type="image/png">
  <link rel="stylesheet" href="/shared-layout.css">
  <script src="https://cdn.auth0.com/js/auth0-spa-js/2.0/auth0-spa-js.production.js"></script>
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
       crossorigin="anonymous"></script>
  <style>
    .gallery-header {
      text-align: center;
      margin-bottom: 40px;
      padding-bottom: 20px;
      border-bottom: 2px solid #e0e0e0;
    }

    .gallery-header h1 {
      color: #2c5aa0;
      margin-bottom: 10px;
    }

    .gallery-header p {
      color: #666;
      font-size: 1.1em;
      margin: 0;
    }

    .gallery-navigation {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-bottom: 30px;
      flex-wrap: wrap;
    }

    .gallery-nav-btn {
      background-color: #2c5aa0;
      color: white;
      padding: 12px 24px;
      border: none;
      border-radius: 6px;
      text-decoration: none;
      font-weight: bold;
      transition: background-color 0.3s ease;
      cursor: pointer;
      display: inline-block;
    }

    .gallery-nav-btn:hover {
      background-color: #1e3a70;
    }

    .gallery-nav-btn.active {
      background-color: #1e3a70;
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .gallery-nav-btn.disabled {
      background-color: #ccc;
      cursor: not-allowed;
      opacity: 0.6;
    }

    .gallery-content {
      min-height: 400px;
      padding: 20px 0;
    }

    .gallery-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .gallery-item {
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 15px;
      text-align: center;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      border: 2px solid transparent;
    }

    .gallery-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      border-color: #2c5aa0;
    }

    .gallery-item img {
      max-width: 100%;
      height: 150px;
      object-fit: contain;
      border-radius: 4px;
      background-color: white;
      border: 1px solid #ddd;
    }

    .gallery-item h3 {
      margin: 10px 0 5px 0;
      color: #2c5aa0;
      font-size: 1em;
    }

    .gallery-item p {
      margin: 0;
      color: #666;
      font-size: 0.9em;
    }

    .coming-soon {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }

    .coming-soon h2 {
      color: #2c5aa0;
      margin-bottom: 15px;
    }

    .coming-soon .icon {
      font-size: 4em;
      margin-bottom: 20px;
      opacity: 0.5;
    }

    .feature-preview {
      background-color: #f0f7ff;
      border: 2px solid #2c5aa0;
      border-radius: 8px;
      padding: 30px;
      margin: 30px 0;
      text-align: center;
    }

    .feature-preview h3 {
      color: #2c5aa0;
      margin-bottom: 15px;
    }

    .feature-list {
      list-style: none;
      padding: 0;
      margin: 20px 0;
    }

    .feature-list li {
      padding: 8px 0;
      color: #555;
    }

    .feature-list li:before {
      content: "🎨 ";
      margin-right: 8px;
    }

    /* Dark theme support */
    body.dark-theme .gallery-item {
      background-color: #2a2a2a;
      color: #e0e0e0;
    }

    body.dark-theme .gallery-item img {
      background-color: #1a1a1a;
      border-color: #444;
    }

    body.dark-theme .feature-preview {
      background-color: #1a2332;
      border-color: #4a90e2;
    }

    body.dark-theme .coming-soon {
      color: #ccc;
    }

    /* Authentication protection */
    .gallery-protected-content {
      display: none; /* Hidden by default until auth check passes */
    }

    .gallery-loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: #ffffff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      color: #333;
    }

    .gallery-loading-overlay .spinner {
      font-size: 3em;
      margin-bottom: 20px;
      animation: spin 1s linear infinite;
    }

    .gallery-loading-overlay h2 {
      margin: 0 0 10px 0;
      font-size: 1.5em;
      color: #2c5aa0;
    }

    .gallery-loading-overlay p {
      margin: 0;
      color: #666;
      font-size: 1.1em;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Dark theme support for loading overlay */
    body.dark-theme .gallery-loading-overlay {
      background-color: #1a1a1a;
      color: #e0e0e0;
    }

    body.dark-theme .gallery-loading-overlay h2 {
      color: #4a90e2;
    }

    body.dark-theme .gallery-loading-overlay p {
      color: #ccc;
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .gallery-navigation {
        flex-direction: column;
        align-items: center;
      }

      .gallery-nav-btn {
        width: 200px;
        text-align: center;
      }

      .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
      }

      .gallery-item img {
        height: 120px;
      }
    }
  </style>
</head>
<body>
  <div id="mobileHeader" style="display: none;">
    <!-- This will be shown on mobile only via JavaScript -->
    <div class="title-container">
      <a href="/">
        <img src="/title.webp" alt="PixelArtNexus" class="title-image">
      </a>
    </div>
    <!-- The login/settings buttons will be positioned here on mobile via JavaScript -->
  </div>

  <!-- Full-screen loading overlay -->
  <div class="gallery-loading-overlay" id="galleryLoadingOverlay">
    <div class="spinner">🔄</div>
    <h2>Checking Access...</h2>
    <p>Verifying your account status...</p>
  </div>

  <div id="fullWidthWrapper">
    <div id="leftBanner" class="banner-div">
      <div class="left-toolbar">
        <div class="title-container">
          <img src="/title.webp" alt="PixelArtNexus" class="title-image">
        </div>
      </div>
      <div class="banner-image-container">
        <img src="/banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- leftbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-****************"
             data-ad-slot="**********"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>

    <div id="mainContainer">
      <!-- Protected content hidden by default -->
      <div class="gallery-protected-content" id="galleryProtectedContent">
        <div class="header">
          <img src="/title.webp" alt="PixelArtNexus" class="logo">
          <div class="gallery-header">
            <h1>PixelArt Gallery</h1>
            <p>Discover amazing pixel art creations from our community</p>
          </div>
        </div>

        <!-- Gallery Navigation -->
        <div class="gallery-navigation">
          <a href="#" class="gallery-nav-btn active" id="featuredBtn">Featured</a>
          <a href="#" class="gallery-nav-btn disabled" id="recentBtn">Recent</a>
          <a href="#" class="gallery-nav-btn disabled" id="popularBtn">Popular</a>
          <a href="#" class="gallery-nav-btn disabled" id="categoriesBtn">Categories</a>
          <a href="#" class="gallery-nav-btn disabled" id="myArtBtn">My Art</a>
        </div>

        <!-- Gallery Content -->
        <div class="gallery-content" id="galleryContent">
        <!-- Coming Soon Message -->
        <div class="coming-soon">
          <div class="icon">🎨</div>
          <h2>Gallery Coming Soon!</h2>
          <p>We're working hard to bring you an amazing gallery experience where you can browse, share, and discover incredible pixel art creations from our community.</p>
        </div>

        <!-- Feature Preview -->
        <div class="feature-preview">
          <h3>What to Expect</h3>
          <ul class="feature-list">
            <li>Browse featured artwork from talented pixel artists</li>
            <li>Discover recent creations and trending pieces</li>
            <li>Explore art by categories (characters, landscapes, icons, etc.)</li>
            <li>Share your own pixel art creations with the community</li>
            <li>Like, comment, and follow your favorite artists</li>
            <li>Download high-quality versions for inspiration</li>
          </ul>
          <p style="margin-top: 20px; color: #666;">
            <strong>Stay tuned!</strong> The gallery will be launching soon with these exciting features and more.
          </p>
        </div>

        <a href="/" class="back-link">← Back to Home</a>
      </div>
    </div>

    <div id="rightBanner" class="banner-div">
      <div class="right-toolbar">
        <button id="authButton" style="display: none;">Login</button>
        <button id="settingsButton">Settings</button>
        
        <!-- Profile Dropdown (hidden by default) -->
        <div class="profile-dropdown" id="profileDropdown" style="display: none;">
          <div id="profileAvatar" class="profile-avatar">
            <img id="profileImage" src="" alt="Profile" class="profile-image">
          </div>
          <div id="profileMenu" class="profile-menu" style="display: none;">
            <div class="profile-menu-item" id="userSettingsMenuItem">User Settings</div>
            <div class="profile-menu-item" id="savedArtMenuItem">Saved Art</div>
            <div class="profile-menu-item" id="logoutMenuItem">Logout</div>
          </div>
        </div>
      </div>
      <div class="banner-image-container">
        <img src="/banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- rightbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-****************"
             data-ad-slot="4302049929"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>
  </div>

  <!-- Settings Modal -->
  <div id="settingsModal" class="modal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Settings</h2>
        <button class="modal-close-btn">&times;</button>
      </div>
      <div class="modal-body">
        <div class="setting-group">
          <label for="themeSelect">Theme:</label>
          <select id="themeSelect">
            <option value="light">Light</option>
            <option value="dark">Dark</option>
          </select>
        </div>
        
        <div id="userProfileSection" style="display: none;">
          <div class="setting-group">
            <label for="usernameInput">Username:</label>
            <input type="text" id="usernameInput" placeholder="Enter your username" maxlength="50">
          </div>
          
          <div class="setting-group">
            <label for="profileImageInput">Profile Image:</label>
            <input type="file" id="profileImageInput" accept="image/png,image/jpeg,image/jpg">
            <img id="profileImagePreview" style="display: none; max-width: 100px; max-height: 100px; margin-top: 10px; border-radius: 50%;">
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button id="saveSettingsBtn" class="btn-primary">Save</button>
        <button id="cancelSettingsBtn" class="btn-secondary">Cancel</button>
      </div>
    </div>
  </div>

  <script src="/shared-layout.js"></script>
  <script>
    // Gallery-specific authentication check
    async function checkGalleryAccess() {
      console.log('🔒 Starting gallery access check...');

      // Wait for Auth0 to initialize with longer timeout
      let attempts = 0;
      const maxAttempts = 100; // 10 seconds max wait

      while (!auth0Client && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }

      // If Auth0 failed to initialize, redirect to account required
      if (!auth0Client) {
        console.log('🚫 Auth0 not available, redirecting to account required page');
        window.location.replace('/account-required');
        return;
      }

      try {
        // Multiple attempts to check authentication
        let authenticated = false;
        let authAttempts = 0;
        const maxAuthAttempts = 3;

        while (authAttempts < maxAuthAttempts) {
          try {
            authenticated = await auth0Client.isAuthenticated();
            console.log(`🔍 Authentication check attempt ${authAttempts + 1}: ${authenticated}`);
            break; // Success, exit retry loop
          } catch (authError) {
            console.warn(`⚠️ Auth check attempt ${authAttempts + 1} failed:`, authError);
            authAttempts++;
            if (authAttempts < maxAuthAttempts) {
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }
        }

        if (!authenticated) {
          console.log('🚫 User not authenticated, redirecting to account required page');
          window.location.replace('/account-required');
          return;
        }

        // Check if user has verified email
        let user = null;
        let userAttempts = 0;
        const maxUserAttempts = 3;

        while (userAttempts < maxUserAttempts) {
          try {
            user = await auth0Client.getUser();
            console.log('👤 User data retrieved:', user ? 'Success' : 'Failed');
            break; // Success, exit retry loop
          } catch (userError) {
            console.warn(`⚠️ User data attempt ${userAttempts + 1} failed:`, userError);
            userAttempts++;
            if (userAttempts < maxUserAttempts) {
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }
        }

        if (!user || !user.email_verified) {
          console.log('🚫 User email not verified, redirecting to account required page');
          console.log('User object:', user);
          console.log('Email verified:', user ? user.email_verified : 'No user');
          window.location.replace('/account-required');
          return;
        }

        console.log('✅ User authenticated and verified, allowing gallery access');
        console.log('User email:', user.email);
        console.log('Email verified:', user.email_verified);

        // Show the protected content and hide loading overlay
        const loadingOverlay = document.getElementById('galleryLoadingOverlay');
        const protectedContent = document.getElementById('galleryProtectedContent');

        if (loadingOverlay) {
          loadingOverlay.style.display = 'none';
        }
        if (protectedContent) {
          protectedContent.style.display = 'block';
        }

      } catch (error) {
        console.error('🚫 Authentication check failed:', error);
        window.location.replace('/account-required');
      }
    }

    // Run authentication check immediately when script loads
    document.addEventListener('DOMContentLoaded', async function() {
      console.log('🚀 Gallery page DOM loaded, starting auth check...');
      await checkGalleryAccess();
    });

    // Also run check after a short delay to catch any late Auth0 initialization
    setTimeout(async () => {
      console.log('🔄 Running delayed gallery auth check...');
      await checkGalleryAccess();
    }, 2000);
  </script>
</body>
</html>
