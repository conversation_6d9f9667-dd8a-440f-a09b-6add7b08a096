# 🔄 Cache Busting Guide for PixelArtNexus

## 🚀 Quick Solutions

### For Immediate Testing (Right Now)
1. **Use the Cache Buster Tool**: Visit `http://localhost:8082/cache-buster.html`
   - This page automatically adds timestamps to all links
   - Click any button to load a fresh version of that page

2. **Manual Browser Cache Clear**:
   - **Chrome/Edge**: `Ctrl+Shift+R` (Windows) or `Cmd+Shift+R` (Mac)
   - **Firefox**: `Ctrl+Shift+R` (Windows) or `Cmd+Shift+R` (Mac)
   - **Safari**: `Cmd+Option+R`

3. **Developer Tools Method**:
   - Press `F12` → Go to Network tab → Check "Disable cache"
   - Keep DevTools open while testing

### For Development
```bash
# Start development server with no caching
npm run dev:no-cache

# Or serve built files with no caching
npm run serve:no-cache
```

## 🛠️ Automatic Cache Busting (For Production)

### How It Works
When you run `npm run build`, the system automatically:
1. Copies all files to `dist/` folder
2. Adds version timestamps to all CSS and JS files
3. Example: `style.css` becomes `style.css?v=1752286709430`

### Build Commands
```bash
# Standard build with cache busting
npm run build

# Production build with obfuscation + cache busting
npm run build:prod

# Safe production build + cache busting
npm run build:safe
```

### What Gets Cache Busted
- ✅ All CSS files (`*.css`)
- ✅ All JavaScript files (`*.js`)
- ✅ Critical images (`title.webp`, `banner.png`, `logo.png`)
- ✅ HTML files get no-cache headers

## 📁 File Structure After Build

```
dist/
├── index.html          (references style.css?v=123456789)
├── canvas.html         (references canvas.css?v=123456789)
├── style.css           (cached for 1 year)
├── canvas.js           (cached for 1 year)
└── gallery/
    └── main.html       (references shared-layout.css?v=123456789)
```

## 🌐 Server Configuration

### .htaccess Rules (Already Configured)
- **HTML files**: No cache, always check for updates
- **CSS/JS with version**: Cache for 1 year (safe because version changes)
- **Localhost**: No caching for development

### Netlify Deployment
When you deploy to Netlify:
1. Run `npm run build:prod` locally
2. Upload the `dist/` folder
3. All files will have proper cache busting

## 🔧 Troubleshooting

### Still Seeing Old Version?
1. **Check the URL**: Look for `?v=` parameters in CSS/JS links
2. **Hard Refresh**: `Ctrl+Shift+R` or `Cmd+Shift+R`
3. **Clear All Data**: Browser Settings → Clear browsing data
4. **Use Incognito**: Open in private/incognito window

### For Testing New Deployments
1. Visit `/cache-buster.html` on your live site
2. Use the timestamped links to test fresh versions
3. Or add `?cb=123456789` to any URL manually

## 📱 Mobile Testing
- **iOS Safari**: Settings → Safari → Clear History and Website Data
- **Android Chrome**: Settings → Privacy → Clear browsing data
- **Or use**: Private browsing mode

## 🎯 Best Practices

### During Development
- Use `npm run dev:no-cache` for local testing
- Keep browser DevTools open with cache disabled
- Use the cache-buster.html tool for quick testing

### Before Deployment
- Always run `npm run build:prod` to get cache busting
- Test the built files with `npm run serve`
- Verify version parameters are added to CSS/JS files

### After Deployment
- Test with cache-buster.html on live site
- Use hard refresh on first visit
- Check browser DevTools Network tab for version parameters

## 🚨 Emergency Cache Clear
If users report seeing old versions:
1. Increment version in `package.json`
2. Run `npm run build:prod`
3. Redeploy to Netlify
4. New version numbers will force cache refresh

---

**💡 Pro Tip**: Bookmark `/cache-buster.html` for quick access to fresh versions during development!
