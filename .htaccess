# Security Headers for PixelArt Nexus

# Content Security Policy
Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.auth0.com https://pagead2.googlesyndication.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://dev-rikve7ck4yromzzs.us.auth0.com https://x8ki-letl-twmt.n7.xano.io; frame-src 'self' https://googleads.g.doubleclick.net; object-src 'none'; base-uri 'self';"

# X-Frame-Options
Header always set X-Frame-Options "SAMEORIGIN"

# X-Content-Type-Options
Header always set X-Content-Type-Options "nosniff"

# X-XSS-Protection
Header always set X-XSS-Protection "1; mode=block"

# Referrer Policy
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Permissions Policy
Header always set Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()"

# Strict Transport Security (HTTPS only)
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

# Remove Server Information
Header unset Server
Header unset X-Powered-By

# Cache Control for Static Assets with version parameters (cache busted)
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|svg|ico|woff|woff2|ttf|eot)$">
    # If file has version parameter (?v=), cache for 1 year
    Header set Cache-Control "public, max-age=31536000, immutable"
</FilesMatch>

# Cache Control for HTML (always check for updates)
<FilesMatch "\.(html|htm)$">
    Header set Cache-Control "public, max-age=0, must-revalidate"
</FilesMatch>

# Special handling for development - disable cache completely for local testing
<If "%{HTTP_HOST} == 'localhost' || %{HTTP_HOST} =~ /^localhost:/">
    <FilesMatch "\.(css|js|html|htm)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
</If>

# Gzip Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Prevent access to sensitive files
<FilesMatch "\.(env|log|md|txt)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent access to .htaccess itself
<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>