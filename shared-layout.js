// Shared JavaScript for About, Contact, Help pages

// Auth0 and theme variables
let auth0Client = null;
let isAuthenticated = false;
let user = null;
let currentUserData = null; // Store complete user data from Xano

// Settings modal state
let settingsChanged = false;
let originalSettings = {};

// Canvas settings variables (needed for Xano compatibility)
let userSettingsId = null;
let currentKeybinds = {};
let showCanvasSizeWarning = true;
let imageImportMode = 'fit-to-view';
let showImageImportWarning = true;
let brushPerformanceMode = false;

// Xano Configuration
const XANO_CONFIG = {
    baseURL: 'https://x8ki-letl-twmt.n7.xano.io/api:YVvnFfHl',
    endpoints: {
        getUserSettings: '/user_settings',
        saveUserSettings: '/user_settings',
        updateUserSettings: '/user_settings'
    }
};

// Xano Service with security improvements
class XanoService {
    static async makeRequest(url, options = {}) {
        // Use secure API client if available
        if (typeof SecurityUtils !== 'undefined' && SecurityUtils.secureAPIClient) {
            try {
                return await SecurityUtils.secureAPIClient.makeJSONRequest(`${XANO_CONFIG.baseURL}${url}`, options);
            } catch (error) {
                console.error('Secure API request failed:', error);
                throw error;
            }
        }
        
        // Fallback to original implementation with basic security
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        };

        const finalOptions = { ...defaultOptions, ...options };
        
        // Add timeout to prevent hanging requests
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);
        finalOptions.signal = controller.signal;
        
        try {
            const response = await fetch(`${XANO_CONFIG.baseURL}${url}`, finalOptions);
            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error('Request timed out. Please try again.');
            }
            throw error;
        }
    }

    static async getUserSettings(userEmail) {
        try {
            const response = await this.makeRequest(`${XANO_CONFIG.endpoints.getUserSettings}?email=${encodeURIComponent(userEmail)}`);
            return response || null;
        } catch (error) {
            console.error('Failed to get user settings from Xano:', error);
            return null;
        }
    }

    static async saveUserSettings(userId, settings, userEmail = null, emailVerified = false, username = null, profileImage = null) {
        try {
            // First, check if a record already exists for this email
            const existingRecord = await this.getUserSettings(userEmail || userId);

            const requestBody = {
                user_id: userId, // Keep for reference, but email is primary
                email: userEmail || userId, // Use email as primary identifier
                username: username,
                profile_image: profileImage,
                theme: settings.theme,
                keybinds: typeof settings.keybinds === 'object' ? JSON.stringify(settings.keybinds) : settings.keybinds,
                email_verified: emailVerified,
                brush_performance_mode: settings.brushPerformanceMode || false,
                show_canvas_size_warning: settings.showCanvasSizeWarning !== undefined ? settings.showCanvasSizeWarning : true,
                image_import_mode: settings.imageImportMode || 'fit-to-view',
                show_image_import_warning: settings.showImageImportWarning !== undefined ? settings.showImageImportWarning : true
            };

            // If we found an existing record, preserve data that shouldn't be overwritten
            if (existingRecord) {
                const existingData = existingRecord.user_settings || existingRecord;
                console.log('🔗 Found existing record for email, linking accounts:', {
                    existingUserId: existingData.user_id,
                    newUserId: userId,
                    email: userEmail
                });

                // Preserve existing username and profile image if not provided
                if (!username && existingData.username) {
                    requestBody.username = existingData.username;
                }
                if (!profileImage && existingData.profile_image) {
                    requestBody.profile_image = existingData.profile_image;
                }

                // Update the user_id to the current login method
                requestBody.user_id = userId;
            }

            console.log('🔍 XanoService.saveUserSettings - Request body:', {
                ...requestBody,
                profile_image: requestBody.profile_image ? `[Base64 data: ${requestBody.profile_image.length} chars]` : null,
                keybinds: typeof requestBody.keybinds === 'object' ? '[Object]' : requestBody.keybinds
            });

            const response = await this.makeRequest(XANO_CONFIG.endpoints.saveUserSettings, {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });

            console.log('🔍 XanoService.saveUserSettings - Response:', {
                ...response,
                profile_image: response.profile_image ? `[Base64 data: ${response.profile_image.length} chars]` : null
            });

            return response;
        } catch (error) {
            console.error('Failed to save user settings to Xano:', error);
            throw error;
        }
    }

    static async checkUsernameAvailability(username) {
        try {
            console.log('🔍 Checking username availability:', username);
            
            const url = `${this.baseURL}/check-username-ability?username=${encodeURIComponent(username)}`;
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                // If endpoint doesn't exist yet, fall back to checking existing users
                if (response.status === 404) {
                    console.log('⚠️ Username availability endpoint not found, using fallback method');
                    return await this.checkUsernameAvailabilityFallback(username);
                }
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('✅ Username availability check result:', result);
            
            // Parse Xano response format:
            // [] = username available
            // [{username: "name"}] = username taken
            const isAvailable = Array.isArray(result) && result.length === 0;
            
            return {
                available: isAvailable,
                message: isAvailable ? 'Username is available' : 'Username already taken'
            };
        } catch (error) {
            console.error('Failed to check username availability:', error);
            // Fall back to checking existing users if API fails
            return await this.checkUsernameAvailabilityFallback(username);
        }
    }

    static async checkUsernameAvailabilityFallback(username) {
        try {
            console.log('🔄 Using fallback method to check username availability');
            
            // Search for existing users with this username
            const url = `${this.baseURL}/user_settings?username=${encodeURIComponent(username)}`;
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();
                // If we get a result, username is taken
                const isTaken = result && (Array.isArray(result) ? result.length > 0 : result.username === username);
                
                return {
                    available: !isTaken,
                    message: isTaken ? 'Username already taken' : 'Username is available'
                };
            } else {
                // If no result found, username is available
                return {
                    available: true,
                    message: 'Username is available'
                };
            }
        } catch (error) {
            console.error('Fallback username check failed:', error);
            // If all else fails, assume available but warn user
            return {
                available: true,
                message: 'Unable to verify username availability. Please try a unique username.',
                warning: true
            };
        }
    }
}

// Theme management
function applyTheme(theme) {
    if (theme === 'dark') {
        document.body.classList.add('dark-theme');
    } else {
        document.body.classList.remove('dark-theme');
    }
}

// Load theme from localStorage
function loadTheme() {
    const savedTheme = localStorage.getItem('pixelart-theme') || 'light';
    applyTheme(savedTheme);
    return savedTheme;
}

// Notification system
function showNotification(message, type = 'success') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());

    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    // Hide and remove notification
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Initialize Auth0
async function initAuth0() {
    try {
        console.log('Initializing Auth0...');
        auth0Client = await auth0.createAuth0Client({
            domain: 'dev-rikve7ck4yromzzs.us.auth0.com',
            clientId: 'ekSg2O7lX2MmmKBJGoiW8OTNFFEg9fWK',
            authorizationParams: {
                redirect_uri: window.location.origin + window.location.pathname,
                scope: 'openid profile email',
                audience: `https://dev-rikve7ck4yromzzs.us.auth0.com/api/v2/`
            },
            cacheLocation: 'localstorage',
            useRefreshTokens: true
        });

        // Handle redirect callback first if present
        if (window.location.search.includes('code=') || window.location.search.includes('state=')) {
            console.log('Handling Auth0 redirect callback...');
            await auth0Client.handleRedirectCallback();
            // Clean up URL
            window.history.replaceState({}, document.title, window.location.pathname);
        }

        // Check if user is authenticated with retry logic
        let authCheckAttempts = 0;
        const maxAuthCheckAttempts = 3;

        while (authCheckAttempts < maxAuthCheckAttempts) {
            try {
                isAuthenticated = await auth0Client.isAuthenticated();
                console.log(`Auth0 authentication status (attempt ${authCheckAttempts + 1}):`, isAuthenticated);

                if (isAuthenticated) {
                    try {
                        const token = await auth0Client.getTokenSilently();
                        user = await auth0Client.getUser();
                        console.log('User authenticated successfully');
                    } catch (tokenError) {
                        console.log('Token error, using basic user info:', tokenError);
                        user = await auth0Client.getUser();
                    }
                    // Load user settings from Xano after authentication
                    await loadUserSettingsFromXano();
                    break; // Exit retry loop on success
                } else {
                    console.log('User not authenticated');
                    break; // Exit retry loop if not authenticated
                }
            } catch (authError) {
                console.warn(`Authentication check failed (attempt ${authCheckAttempts + 1}):`, authError);
                authCheckAttempts++;
                if (authCheckAttempts < maxAuthCheckAttempts) {
                    await new Promise(resolve => setTimeout(resolve, 500)); // Wait 500ms before retry
                }
            }
        }

        // Always update UI after checking authentication state
        updateAuthButton();

    } catch (error) {
        console.error('Auth0 initialization error:', error);
        // Reset auth state on error
        isAuthenticated = false;
        user = null;
        // Ensure UI is updated even if auth fails
        updateAuthButton();
    }
}

// Load user settings from Xano
async function loadUserSettingsFromXano() {
    if (!isAuthenticated || !user) {
        console.log('User not authenticated, skipping Xano settings load');
        return;
    }

    try {
        console.log('Loading user settings from Xano...');

        // Use email as primary identifier to link accounts across auth methods
        const userEmail = user.email || user.sub;
        console.log('Looking up settings by email:', userEmail);
        const xanoSettings = await XanoService.getUserSettings(userEmail);

        if (xanoSettings) {
            // Handle nested response structure from Xano
            const settingsData = xanoSettings.user_settings || xanoSettings;
            currentUserData = settingsData; // Store complete user data
            userSettingsId = settingsData.id;

            console.log('Loaded settings from Xano:', settingsData);
            console.log('🔍 Loaded username:', settingsData.username);
            console.log('🔍 Loaded profile image:', !!settingsData.profile_image);

            // Initialize canvas variables from Xano data
            if (settingsData.keybinds) {
                try {
                    currentKeybinds = typeof settingsData.keybinds === 'string' ?
                        JSON.parse(settingsData.keybinds) : settingsData.keybinds;
                } catch (e) {
                    console.warn('Failed to parse keybinds from Xano:', e);
                    currentKeybinds = {};
                }
            }

            brushPerformanceMode = settingsData.brush_performance_mode || false;
            showCanvasSizeWarning = settingsData.show_canvas_size_warning !== false;
            imageImportMode = settingsData.image_import_mode || 'fit-to-view';
            showImageImportWarning = settingsData.show_image_import_warning !== false;

            // Apply theme from Xano if available, but only if different from localStorage
            if (settingsData.theme) {
                const localTheme = localStorage.getItem('pixelart-theme') || 'light';
                if (settingsData.theme !== localTheme) {
                    console.log(`Theme mismatch: local=${localTheme}, xano=${settingsData.theme}. Using Xano theme.`);
                    applyTheme(settingsData.theme);
                    localStorage.setItem('pixelart-theme', settingsData.theme);
                } else {
                    console.log(`Theme matches: using ${localTheme}`);
                }
            }

            console.log('Successfully applied settings from Xano');
        } else {
            console.log('No settings found in Xano for this user');
        }
    } catch (error) {
        console.error('Failed to load settings from Xano:', error);
        console.log('Falling back to local settings');
    }
}

// Update authentication button display
function updateAuthButton() {
    const authButton = document.getElementById('authButton');
    const settingsButton = document.getElementById('settingsButton');
    const profileDropdown = document.getElementById('profileDropdown');

    if (!authButton) return; // Exit if elements don't exist

    if (isAuthenticated && user) {
        // User is authenticated
        if (user.email_verified) {
            // Email is verified - show profile dropdown
            authButton.style.display = 'none';
            settingsButton.style.display = 'none';
            profileDropdown.style.display = 'block';

            // Update profile image - use Xano custom image or default blank
            const profileImage = document.getElementById('profileImage');
            if (profileImage) {
                if (currentUserData && currentUserData.profile_image) {
                    console.log('🔍 Setting profile image from Xano currentUserData');
                    profileImage.src = currentUserData.profile_image;
                } else {
                    console.log('🔍 Setting default blank profile image');
                    // Set default blank profile image (same as main app)
                    profileImage.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFMEUwRTAiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMiIgZmlsbD0iIzk5OTk5OSIvPgo8L3N2Zz4K';
                }
            }
        } else {
            // Email not verified - show login button with verification message
            authButton.style.display = 'block';
            authButton.textContent = 'Verify Email';
            settingsButton.style.display = 'none';
            profileDropdown.style.display = 'none';
        }
    } else {
        // User not authenticated - show login and settings buttons
        authButton.style.display = 'block';
        authButton.textContent = 'Login';
        settingsButton.style.display = 'block';
        profileDropdown.style.display = 'none';
    }
}

// Handle login
async function handleLogin() {
    // Check if we're on a page that's not authorized for Auth0 callbacks
    const currentPath = window.location.pathname;
    const unauthorizedPages = ['/account-required'];

    if (unauthorizedPages.includes(currentPath)) {
        console.log('Redirecting to login page from unauthorized callback page:', currentPath);
        window.location.href = '/login';
        return;
    }

    if (!auth0Client) {
        showNotification('Authentication not available', 'error');
        return;
    }

    try {
        if (isAuthenticated && user && !user.email_verified) {
            // User needs to verify email
            showNotification('Please check your email and verify your account', 'error');
            return;
        }

        await auth0Client.loginWithRedirect();
    } catch (error) {
        console.error('Login error:', error);
        showNotification('Login failed. Please try again.', 'error');
    }
}

// Handle logout
async function handleLogout() {
    if (!auth0Client) return;

    try {
        await auth0Client.logout({
            logoutParams: {
                returnTo: window.location.origin
            }
        });
    } catch (error) {
        console.error('Logout error:', error);
        showNotification('Logout failed. Please try again.', 'error');
    }
}

// Settings modal functionality
function openSettingsModal() {
    const modal = document.getElementById('settingsModal');
    if (modal) {
        // Store original settings
        originalSettings = {
            theme: localStorage.getItem('pixelart-theme') || 'light',
            username: currentUserData ? currentUserData.username : '',
            profileImage: currentUserData ? currentUserData.profile_image : null
        };

        // Load current settings into modal
        loadSettingsIntoModal();

        // Reset change tracking
        settingsChanged = false;

        modal.style.display = 'flex';
        console.log('⚙️ Settings modal opened');
    }
}

function loadSettingsIntoModal() {
    // Load theme
    const themeSelect = document.getElementById('themeSelect');
    if (themeSelect) {
        themeSelect.value = originalSettings.theme;
    }

    // Show/hide user profile section based on authentication
    const userProfileSection = document.getElementById('userProfileSection');
    if (userProfileSection) {
        if (isAuthenticated && user && user.email_verified) {
            userProfileSection.style.display = 'block';

            const usernameInput = document.getElementById('usernameInput');
            const profileImagePreview = document.getElementById('profileImagePreview');

            if (usernameInput) {
                usernameInput.value = originalSettings.username || '';
            }

            if (profileImagePreview && originalSettings.profileImage) {
                profileImagePreview.src = originalSettings.profileImage;
                profileImagePreview.style.display = 'block';
            } else if (profileImagePreview) {
                profileImagePreview.style.display = 'none';
            }
        } else {
            userProfileSection.style.display = 'none';
        }
    }
}

async function closeSettingsModal(force = false) {
    if (!force && settingsChanged) {
        // Show confirmation dialog
        const shouldSave = confirm('You have unsaved changes. Would you like to save your settings before closing?');
        if (shouldSave) {
            await saveSettings();
            return;
        }

        // User chose not to save, revert changes
        revertSettings();
    }

    const modal = document.getElementById('settingsModal');
    if (modal) {
        modal.style.display = 'none';
        console.log('⚙️ Settings modal closed');
    }
}

function revertSettings() {
    // Revert theme
    applyTheme(originalSettings.theme);
    localStorage.setItem('pixelart-theme', originalSettings.theme);

    console.log('🔄 Settings reverted to original values');
}

async function saveSettings() {
    try {
        const themeSelect = document.getElementById('themeSelect');
        const selectedTheme = themeSelect ? themeSelect.value : 'light';

        // Apply theme immediately
        applyTheme(selectedTheme);
        localStorage.setItem('pixelart-theme', selectedTheme);

        // Save to database if user is authenticated and verified
        if (isAuthenticated && user && user.email_verified) {
            await saveSettingsToXano(selectedTheme);
        }

        // Reset change tracking
        settingsChanged = false;
        originalSettings.theme = selectedTheme;

        console.log('💾 Settings saved - theme:', selectedTheme);
        showNotification('Settings saved successfully!');

        // Close modal
        const modal = document.getElementById('settingsModal');
        if (modal) {
            modal.style.display = 'none';
        }

    } catch (error) {
        console.error('Failed to save settings:', error);
        
        // Check if this is a username availability error or user cancellation
        if (error.message && error.message.includes('Username change cancelled by user')) {
            showNotification('Settings save cancelled.', 'info');
        } else if (error.message && (error.message.includes('Username already taken') || error.message.includes('already taken'))) {
            showNotification('Username already taken. Please choose a different username.', true);
        } else if (error.message && error.message.toLowerCase().includes('username')) {
            showNotification(error.message, true);
        } else {
            showNotification('Failed to save settings. Please try again.', true);
        }
    }
}

async function saveSettingsToXano(theme) {
    if (!isAuthenticated || !user) {
        console.log('User not authenticated, skipping Xano save');
        return;
    }

    // Check if user email is verified
    const emailVerified = user.email_verified || false;
    if (!emailVerified) {
        console.log('User email not verified, skipping cloud sync');
        return;
    }

    try {
        // Get current values from modal
        const usernameInput = document.getElementById('usernameInput');
        const profileImageInput = document.getElementById('profileImageInput');

        let username = currentUserData ? currentUserData.username : null;
        let profileImageData = currentUserData ? currentUserData.profile_image : null;

        // Update username if changed with validation
        if (usernameInput && usernameInput.value.trim() !== originalSettings.username) {
            const newUsername = usernameInput.value.trim();
            
            // Validate username using security utils if available
            if (typeof SecurityUtils !== 'undefined' && SecurityUtils.InputValidator) {
                const validation = SecurityUtils.InputValidator.validateUsername(newUsername);
                if (!validation.valid) {
                    showNotification(validation.error, 'error');
                    return;
                }
            } else {
                // Fallback validation
                if (newUsername.length > 50) {
                    showNotification('Username cannot exceed 50 characters', 'error');
                    return;
                }
                if (!/^[a-zA-Z0-9 _\-\.]+$/.test(newUsername)) {
                    showNotification('Username can only contain letters, numbers, spaces, underscores, hyphens, and periods', 'error');
                    return;
                }
            }

            // Check username availability
            try {
                showNotification('Checking username availability...', 'info');
                const availabilityCheck = await XanoService.checkUsernameAvailability(newUsername);
                if (!availabilityCheck.available) {
                    // Throw an error instead of just returning, so it propagates to the main catch block
                    throw new Error(availabilityCheck.message);
                }
                
                if (availabilityCheck.warning) {
                    const proceed = confirm(availabilityCheck.message + '\n\nDo you want to proceed anyway?');
                    if (!proceed) {
                        throw new Error('Username change cancelled by user');
                    }
                }
            } catch (error) {
                console.error('Username availability check failed:', error);
                // Re-throw the error so it's caught by the main saveSettings function
                throw error;
            }
            
            username = newUsername;
        }

        // Update profile image if changed with validation
        if (profileImageInput && profileImageInput.files && profileImageInput.files[0]) {
            const file = profileImageInput.files[0];
            
            // Validate file using security utils if available
            if (typeof SecurityUtils !== 'undefined' && SecurityUtils.InputValidator) {
                const validation = SecurityUtils.InputValidator.validateFileUpload(file);
                if (!validation.valid) {
                    showNotification(validation.error, 'error');
                    return;
                }
            } else {
                // Fallback validation
                const maxSizeBytes = 10 * 1024 * 1024; // 10MB
                if (file.size > maxSizeBytes) {
                    showNotification('File size cannot exceed 10MB', 'error');
                    return;
                }
                
                const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
                if (!allowedTypes.includes(file.type)) {
                    showNotification('Invalid file type. Only PNG, JPEG, GIF, and WebP are allowed', 'error');
                    return;
                }
            }
            
            profileImageData = await convertImageToBase64(file);
        }

        // Prepare settings data - include all user preferences (matching canvas implementation)
        const settings = {
            theme,
            keybinds: currentKeybinds,
            brushPerformanceMode: brushPerformanceMode,
            showCanvasSizeWarning: showCanvasSizeWarning,
            imageImportMode: imageImportMode,
            showImageImportWarning: showImageImportWarning
        };

        const userEmail = user.email || user.sub;

        // Preserve existing username and profile image from currentUserData
        const existingUsername = username || (currentUserData ? currentUserData.username : null);
        const existingProfileImage = profileImageData || (currentUserData ? currentUserData.profile_image : null);

        console.log('Full user object when saving:', user); // Debug: Full user object
        console.log('Saving with email:', userEmail); // Debug: Check what email we're sending
        console.log('User sub (Auth0 ID):', user.sub); // Debug: Check Auth0 ID
        console.log('Email verified status:', emailVerified); // Debug: Check verification status
        console.log('Preserving existing username:', existingUsername); // Debug: Check preserved username
        console.log('Preserving existing profile image:', !!existingProfileImage); // Debug: Check preserved image

        const response = await XanoService.saveUserSettings(
            user.sub,
            settings,
            userEmail,
            emailVerified,
            existingUsername, // Preserve existing username
            existingProfileImage // Preserve existing profile image
        );

        // Handle nested response structure from Xano
        const responseData = response.user_settings || response;
        userSettingsId = responseData.id;
        currentUserData = responseData; // Update currentUserData with the response

        console.log('✅ saveSettingsToXano - Updated currentUserData:', {
            id: currentUserData.id,
            username: currentUserData.username,
            hasProfileImage: !!currentUserData.profile_image,
            allKeys: Object.keys(currentUserData)
        });

        // Update original settings
        originalSettings.username = existingUsername;
        originalSettings.profileImage = existingProfileImage;

        // Update profile dropdown if visible
        updateAuthButton();

        console.log('Saved settings to Xano');

    } catch (error) {
        console.error('Failed to save settings to Xano:', error);
        // Don't throw error - let local storage still work
        throw error;
    }
}

// Helper functions
function convertImageToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
}

function onThemeChange() {
    const themeSelect = document.getElementById('themeSelect');
    if (themeSelect) {
        const selectedTheme = themeSelect.value;

        // Apply theme immediately
        applyTheme(selectedTheme);

        // Mark as changed
        settingsChanged = true;

        console.log('🎨 Theme changed to:', selectedTheme);
    }
}

let usernameCheckTimeout = null;

function onUsernameChange(event) {
    settingsChanged = true;
    console.log('👤 Username changed');
    
    const usernameInput = event.target;
    const username = usernameInput.value.trim();
    
    // Clear previous timeout
    if (usernameCheckTimeout) {
        clearTimeout(usernameCheckTimeout);
    }
    
    // Basic validation first
    if (typeof SecurityUtils !== 'undefined' && SecurityUtils.InputValidator) {
        const validation = SecurityUtils.InputValidator.validateUsername(username);
        if (!validation.valid) {
            usernameInput.setCustomValidity(validation.error);
            return;
        }
    } else {
        // Fallback validation if security utils not available
        if (username.length > 50) {
            usernameInput.setCustomValidity('Username cannot exceed 50 characters');
            return;
        }
        if (username && !/^[a-zA-Z0-9 _\-\.]+$/.test(username)) {
            usernameInput.setCustomValidity('Username can only contain letters, numbers, spaces, underscores, hyphens, and periods');
            return;
        }
    }
    
    // Clear validation if basic checks pass
    usernameInput.setCustomValidity('');
    
    // Check availability after a delay (debouncing)
    if (username && username !== (originalSettings ? originalSettings.username : '')) {
        usernameCheckTimeout = setTimeout(async () => {
            try {
                const availabilityCheck = await XanoService.checkUsernameAvailability(username);
                if (!availabilityCheck.available) {
                    usernameInput.setCustomValidity(availabilityCheck.message);
                } else if (availabilityCheck.warning) {
                    usernameInput.setCustomValidity(availabilityCheck.message);
                } else {
                    usernameInput.setCustomValidity('');
                }
            } catch (error) {
                console.warn('Username availability check failed:', error);
                // Don't set validation error for network issues
            }
        }, 1000); // 1 second delay
    }
}

function onProfileImageChange() {
    const input = document.getElementById('profileImageInput');
    const preview = document.getElementById('profileImagePreview');

    if (input && input.files && input.files[0]) {
        const file = input.files[0];

        // Validate file type
        if (!file.type.match(/^image\/(png|jpg|jpeg)$/)) {
            showNotification('Please select a PNG or JPG image file.', true);
            input.value = '';
            return;
        }

        // Validate file size (max 2MB)
        if (file.size > 2 * 1024 * 1024) {
            showNotification('Image file must be smaller than 2MB.', true);
            input.value = '';
            return;
        }

        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            if (preview) {
                preview.src = e.target.result;
                preview.style.display = 'block';
            }
        };
        reader.readAsDataURL(file);

        settingsChanged = true;
        console.log('🖼️ Profile image changed');
    }
}

// Handle settings (open settings modal)
function handleSettings() {
    openSettingsModal();
}

// Handle user settings (open settings modal)
function handleUserSettings() {
    openSettingsModal();
}

// Handle saved art (redirect to main page)
function handleSavedArt() {
    window.location.href = '/?saved-art=true';
}

// Profile dropdown toggle
function toggleProfileMenu(event) {
    // Get the clicked profile avatar
    const clickedAvatar = event.currentTarget || this;
    
    // Find the closest profile dropdown container
    const profileDropdown = clickedAvatar.closest('.profile-dropdown');
    
    if (profileDropdown) {
        // Find the profile menu within this dropdown
        const profileMenu = profileDropdown.querySelector('.profile-menu');
        
        if (profileMenu) {
            const isVisible = profileMenu.style.display === 'block';
            profileMenu.style.display = isVisible ? 'none' : 'block';
            
            // If we're showing the menu, update its position
            if (!isVisible) {
                // If this is in the mobile header, ensure proper positioning
                if (clickedAvatar.closest('#mobileHeader')) {
                    profileMenu.style.position = 'absolute';
                    profileMenu.style.top = '40px';
                    profileMenu.style.right = '0';
                    profileMenu.style.zIndex = '1001';
                }
            }
        }
    }
}

// Close profile menu when clicking outside
function closeProfileMenuOnClickOutside(event) {
    // Close all profile menus if clicking outside any profile dropdown
    const profileMenus = document.querySelectorAll('.profile-menu');
    const profileDropdowns = document.querySelectorAll('.profile-dropdown');
    
    profileMenus.forEach(menu => {
        if (menu.style.display === 'block') {
            // Check if the click was outside all profile dropdowns
            let outsideClick = true;
            profileDropdowns.forEach(dropdown => {
                if (dropdown.contains(event.target)) {
                    outsideClick = false;
                }
            });
            
            if (outsideClick) {
                menu.style.display = 'none';
            }
        }
    });
}

// Notification system
function showNotification(message, isError = false) {
    // Remove existing notification if any
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // Create new notification
    const notification = document.createElement('div');
    notification.className = `notification ${isError ? 'error' : ''}`;
    notification.textContent = message;
    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Hide and remove notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Handle mobile layout
function handleMobileLayout() {
    const mobileHeader = document.getElementById('mobileHeader');
    const rightToolbar = document.querySelector('#rightBanner .right-toolbar');
    const rightBanner = document.getElementById('rightBanner');
    
    if (!mobileHeader || !rightToolbar) return;
    
    // Function to check if we're in mobile view
    const isMobileView = () => window.innerWidth <= 1000;
    
    // Function to update layout based on screen size
    const updateLayout = () => {
        if (isMobileView()) {
            // Show mobile header
            mobileHeader.style.display = 'flex';
            
            // Clone the right toolbar buttons to the mobile header
            const mobileToolbar = rightToolbar.cloneNode(true);
            
            // Clear any existing buttons in mobile header (except the title)
            while (mobileHeader.children.length > 1) {
                mobileHeader.removeChild(mobileHeader.lastChild);
            }
            
            // Add the cloned toolbar to mobile header
            mobileHeader.appendChild(mobileToolbar);
            
            // Add event listeners to the cloned buttons
            const clonedAuthButton = mobileToolbar.querySelector('#authButton');
            const clonedSettingsButton = mobileToolbar.querySelector('#settingsButton');
            const clonedProfileDropdown = mobileToolbar.querySelector('.profile-dropdown');
            const clonedProfileAvatar = mobileToolbar.querySelector('#profileAvatar');
            
            if (clonedAuthButton) {
                clonedAuthButton.addEventListener('click', handleLogin);
            }
            
            if (clonedSettingsButton) {
                clonedSettingsButton.addEventListener('click', handleSettings);
            }
            
            if (clonedProfileAvatar) {
                clonedProfileAvatar.addEventListener('click', toggleProfileMenu);
            }
            
            // Ensure the profile menu items in the cloned dropdown have proper event listeners
            if (clonedProfileDropdown) {
                const clonedUserSettingsMenuItem = clonedProfileDropdown.querySelector('#userSettingsMenuItem');
                const clonedSavedArtMenuItem = clonedProfileDropdown.querySelector('#savedArtMenuItem');
                const clonedLogoutMenuItem = clonedProfileDropdown.querySelector('#logoutMenuItem');
                
                if (clonedUserSettingsMenuItem) {
                    clonedUserSettingsMenuItem.addEventListener('click', handleUserSettings);
                }
                
                if (clonedSavedArtMenuItem) {
                    clonedSavedArtMenuItem.addEventListener('click', handleSavedArt);
                }
                
                if (clonedLogoutMenuItem) {
                    clonedLogoutMenuItem.addEventListener('click', handleLogout);
                }
            }
            
            // Hide the right banner completely
            if (rightBanner) {
                rightBanner.style.display = 'none';
            }
        } else {
            // Hide mobile header
            mobileHeader.style.display = 'none';
            
            // Show the right banner
            if (rightBanner) {
                rightBanner.style.display = 'flex';
            }
        }
    };
    
    // Update layout on page load
    updateLayout();
    
    // Update layout on window resize
    window.addEventListener('resize', updateLayout);
}

// Initialize page
function initializePage() {
    // Load theme first - this ensures theme is applied immediately
    loadTheme();

    // Set up event listeners
    const authButton = document.getElementById('authButton');
    const settingsButton = document.getElementById('settingsButton');
    const profileAvatar = document.getElementById('profileAvatar');
    const userSettingsMenuItem = document.getElementById('userSettingsMenuItem');
    const savedArtMenuItem = document.getElementById('savedArtMenuItem');
    const logoutMenuItem = document.getElementById('logoutMenuItem');

    if (authButton) {
        authButton.addEventListener('click', handleLogin);
    }

    if (settingsButton) {
        settingsButton.addEventListener('click', handleSettings);
    }

    if (profileAvatar) {
        profileAvatar.addEventListener('click', toggleProfileMenu);
    }

    if (userSettingsMenuItem) {
        userSettingsMenuItem.addEventListener('click', handleUserSettings);
    }

    if (savedArtMenuItem) {
        savedArtMenuItem.addEventListener('click', handleSavedArt);
    }

    if (logoutMenuItem) {
        logoutMenuItem.addEventListener('click', handleLogout);
    }

    // Settings modal event listeners
    const settingsModal = document.getElementById('settingsModal');
    if (settingsModal) {
        // Close button
        const closeBtn = settingsModal.querySelector('.modal-close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => closeSettingsModal());
        }

        // Save button
        const saveBtn = document.getElementById('saveSettingsBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', saveSettings);
        }

        // Cancel button
        const cancelBtn = document.getElementById('cancelSettingsBtn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => closeSettingsModal());
        }

        // Close modal when clicking outside
        settingsModal.addEventListener('click', function(e) {
            if (e.target === settingsModal) {
                closeSettingsModal();
            }
        });

        // Theme change listener
        const themeSelect = document.getElementById('themeSelect');
        if (themeSelect) {
            themeSelect.addEventListener('change', onThemeChange);
        }

        // Username change listener
        const usernameInput = document.getElementById('usernameInput');
        if (usernameInput) {
            usernameInput.addEventListener('input', onUsernameChange);
        }

        // Profile image change listener
        const profileImageInput = document.getElementById('profileImageInput');
        if (profileImageInput) {
            profileImageInput.addEventListener('change', onProfileImageChange);
        }
    }

    // Close profile menu when clicking outside
    document.addEventListener('click', closeProfileMenuOnClickOutside);

    // Add click handler for logo to navigate to home
    const leftToolbar = document.querySelector('.left-toolbar');
    if (leftToolbar) {
        leftToolbar.addEventListener('click', function() {
            window.location.href = '/';
        });
    }

    // Initialize Auth0 if available
    if (typeof auth0 !== 'undefined' && auth0.createAuth0Client) {
        initAuth0();
    } else {
        console.log('Auth0 not available - running in offline mode');
        // Ensure theme is applied even in offline mode
        loadTheme();
        updateAuthButton();
    }
    
    // Handle mobile layout
    handleMobileLayout();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializePage);
