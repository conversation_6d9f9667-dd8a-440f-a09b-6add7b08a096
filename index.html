<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="google-adsense-account" content="ca-pub-****************">
  <title>PixelArtNexus | Professional Pixel Art Creation Platform</title>
  <link rel="icon" href="/logo.png" type="image/png">
  <link rel="stylesheet" href="index.css">
  <script src="runtime-config.js"></script>
  <script src="https://cdn.auth0.com/js/auth0-spa-js/2.0/auth0-spa-js.production.js"></script>
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
       crossorigin="anonymous"></script>
</head>
<body>
  <div id="mobileHeader" style="display: none;">
    <!-- This will be shown on mobile only via JavaScript -->
    <div class="title-container">
      <a href="/">
        <img src="title.webp" alt="PixelArtNexus" class="title-image">
      </a>
    </div>
    <!-- The login/settings buttons will be positioned here on mobile via JavaScript -->
  </div>
  
  <div id="fullWidthWrapper">
    <div id="leftBanner" class="banner-div">
      <div class="left-toolbar">
        <div class="title-container">
          <img src="title.webp" alt="PixelArtNexus" class="title-image">
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- leftbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-****************"
             data-ad-slot="6832096065"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>

    <div id="mainContainer">
      <!-- Welcome Header -->
      <div class="welcome-header">
        <img src="title.webp" alt="PixelArtNexus" class="main-logo">
        <h1>Welcome to PixelArtNexus</h1>
        <div class="quote-section">
          <blockquote>
            "Every pixel tells a story. Every canvas holds infinite possibilities."
          </blockquote>
          <p class="mission-statement">
            Empowering artists worldwide with professional-grade pixel art tools, 
            accessible from any device, completely free.
          </p>
        </div>
      </div>

      <!-- Navigation Buttons -->
      <div class="main-navigation">
        <div class="nav-button-container">
          <a href="/canvas" class="nav-button primary">
            <div class="nav-button-icon">🎨</div>
            <div class="nav-button-content">
              <h3>Canvas Editor</h3>
              <p>Create stunning pixel art with our full-featured editor</p>
            </div>
          </a>
          
          <button class="nav-button secondary" id="animationStudioBtn">
            <div class="nav-button-icon">🎬</div>
            <div class="nav-button-content">
              <h3>Animation Studio</h3>
              <p>Bring your pixel art to life with animations</p>
              <span class="coming-soon-badge">Coming Soon</span>
            </div>
          </button>
          
          <a href="/gallery/main" class="nav-button secondary">
            <div class="nav-button-icon">🖼️</div>
            <div class="nav-button-content">
              <h3>Community Gallery</h3>
              <p>Explore and share amazing pixel art creations (Account Required)</p>
            </div>
          </a>
        </div>
      </div>

      <!-- Features Preview -->
      <div class="features-preview">
        <h2>Why Choose PixelArtNexus?</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">⚡</div>
            <h4>Lightning Fast</h4>
            <p>Optimized performance for smooth drawing on any device</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🔧</div>
            <h4>Professional Tools</h4>
            <p>Advanced brushes, layers, and editing capabilities</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">☁️</div>
            <h4>Cloud Sync</h4>
            <p>Save your work and sync settings across all devices</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🎯</div>
            <h4>Always Free</h4>
            <p>Full access to all features without any cost</p>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="bottom-toolbar">
        <div class="footer-section core-info">
          <a href="/about" class="footer-link">About</a>
          <a href="/contact" class="footer-link">Contact</a>
          <a href="https://docs.google.com/forms/d/e/1FAIpQLSdzeaR8oDUKpDuSFuKWGfuN9B7LC0Ti_KU0LUw3_tfQgwW-wg/viewform?usp=dialog" target="_blank" class="footer-link">Feedback</a>
          <a href="/help" class="footer-link">Help / FAQ</a>
        </div>
        <div class="footer-section legal-policy">
          <a href="/terms" class="footer-link">Terms of Service</a>
          <a href="/privacy" class="footer-link">Privacy Policy</a>
        </div>
        <div class="footer-section community-social">
          <a href="/community-guidelines" class="footer-link">Community Guidelines</a>
          <a href="/social" class="footer-link">Social Media</a>
          <a href="/blog" class="footer-link">Blog / Updates</a>
        </div>
        <div class="footer-section copyright">
          <span class="copyright-text">© 2025 PixelArtNexus. All rights reserved.</span>
        </div>
      </div>
    </div>

    <div id="rightBanner" class="banner-div">
      <div class="right-toolbar">
        <!-- Login button (shown when not authenticated) -->
        <button id="authButton" class="auth-button">Login</button>

        <!-- Settings button (shown when not authenticated) -->
        <button id="settingsButton" class="settings-button" style="display: none;">Settings</button>

        <!-- Profile dropdown (shown when authenticated and verified) -->
        <div id="profileDropdown" class="profile-dropdown" style="display: none;">
          <div id="profileAvatar" class="profile-avatar">
            <img id="profileImage" src="" alt="Profile" class="profile-image">
          </div>
          <div id="profileMenu" class="profile-menu" style="display: none;">
            <div class="profile-menu-item" id="userSettingsMenuItem">User Settings</div>
            <div class="profile-menu-item" id="savedArtMenuItem">Saved Art</div>
            <div class="profile-menu-item" id="logoutMenuItem">Logout</div>
          </div>
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- rightbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-****************"
             data-ad-slot="4302049929"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>
  </div>

  <!-- Settings Modal -->
  <div id="settingsModal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
      <button type="button" class="modal-close-btn" aria-label="Close">&times;</button>
      <h2>Settings</h2>

      <!-- Theme Settings -->
      <div class="settings-section">
        <h3>Appearance</h3>
        <div class="setting-item">
          <div>
            <label for="themeSelect">Theme:</label>
            <select id="themeSelect">
              <option value="light">Light Mode</option>
              <option value="dark">Dark Mode</option>
            </select>
          </div>
        </div>
      </div>

      <!-- User Profile Settings (shown only for authenticated users) -->
      <div id="userProfileSection" class="settings-section" style="display: none;">
        <h3>Profile</h3>
        <div class="setting-item">
          <div>
            <label for="usernameInput">Username:</label>
            <input type="text" id="usernameInput" placeholder="Enter username" maxlength="50">
          </div>
        </div>
        <div class="setting-item">
          <div>
            <label for="profileImageInput">Profile Picture:</label>
            <input type="file" id="profileImageInput" accept="image/png,image/jpg,image/jpeg">
            <img id="profileImagePreview" style="display: none; width: 40px; height: 40px; border-radius: 50%; margin-top: 10px;">
          </div>
        </div>
      </div>

      <!-- Modal Buttons -->
      <div class="modal-buttons">
        <button type="button" id="cancelSettingsBtn" class="modal-button secondary">Cancel</button>
        <button type="button" id="saveSettingsBtn" class="modal-button primary">Save Settings</button>
      </div>
    </div>
  </div>

  <!-- Coming Soon Modal -->
  <div id="comingSoonModal" class="modal-overlay" style="display: none;">
    <div class="modal-content coming-soon-modal">
      <button type="button" class="modal-close-btn" aria-label="Close">&times;</button>
      <div class="coming-soon-content">
        <div class="coming-soon-icon">🚀</div>
        <h2 id="comingSoonTitle">Feature Coming Soon!</h2>
        <p id="comingSoonDescription">We're working hard to bring you this amazing feature.</p>
        <p class="coming-soon-details">
          Stay tuned for updates! Follow our progress and be the first to know when new features launch.
        </p>
        <div class="coming-soon-actions">
          <a href="https://docs.google.com/forms/d/e/1FAIpQLSdzeaR8oDUKpDuSFuKWGfuN9B7LC0Ti_KU0LUw3_tfQgwW-wg/viewform?usp=dialog" target="_blank" class="feedback-btn">
            📝 Request Features
          </a>
        </div>
      </div>
    </div>
  </div>

  <script src="error-handler.js"></script>
  <script src="security-utils.js"></script>
  <script src="index.js"></script>
</body>
</html>
