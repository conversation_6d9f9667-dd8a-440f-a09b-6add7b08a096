{"name": "pixel-art-nexus", "version": "1.0.0", "description": "A pixel art editor with layers and multiple canvases", "main": "index.js", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "copy-assets": "copyfiles -u 0 canvas.html about.html contact.html main.html canvas.css ads.txt social.html blog.html logo.png community-guidelines.html index.html terms.html privacy.html index.css login.html canvas.js security-utils.js error-handler.js shared-layout.css shared-layout.js robots.txt sitemap.xml favicon.ico twitter-card-image.jpg opengraph-image.jpg og:image webmanifest icon.svg icon_192x192.png icon_512x512.png apple-touch-icon.png android-chrome-192x192.png android-chrome-512x512.png maskable_icon_x title.webp banner.png runtime-config.js manifest.json sw.js .htaccess dist/", "copy-js": "copyfiles -u 0 index.js dist/", "generate-config": "node scripts/generate-config.js", "build": "npm run clean && npm run generate-config && npm run copy-assets && npm run copy-js", "obfuscate:safe": "javascript-obfuscator dist/index.js --output dist/index.js --compact true --control-flow-flattening false --dead-code-injection false --debug-protection false --disable-console-output false --identifier-names-generator hexadecimal --rename-globals false --self-defending false --string-array true --string-array-threshold 0.5 --transform-object-keys false --unicode-escape-sequence false && javascript-obfuscator dist/canvas.js --output dist/canvas.js --compact true --control-flow-flattening false --dead-code-injection false --debug-protection false --disable-console-output false --identifier-names-generator hexadecimal --rename-globals false --self-defending false --string-array true --string-array-threshold 0.5 --transform-object-keys false --unicode-escape-sequence false && javascript-obfuscator dist/security-utils.js --output dist/security-utils.js --compact true --control-flow-flattening false --dead-code-injection false --debug-protection false --disable-console-output false --identifier-names-generator hexadecimal --rename-globals false --self-defending false --string-array true --string-array-threshold 0.5 --transform-object-keys false --unicode-escape-sequence false && javascript-obfuscator dist/error-handler.js --output dist/error-handler.js --compact true --control-flow-flattening false --dead-code-injection false --debug-protection false --disable-console-output false --identifier-names-generator hexadecimal --rename-globals false --self-defending false --string-array true --string-array-threshold 0.5 --transform-object-keys false --unicode-escape-sequence false", "obfuscate": "javascript-obfuscator dist/index.js --output dist/index.js --compact true --identifier-names-generator hexadecimal --string-array false && javascript-obfuscator dist/canvas.js --output dist/canvas.js --compact true --identifier-names-generator hexadecimal --string-array false && javascript-obfuscator dist/security-utils.js --output dist/security-utils.js --compact true --identifier-names-generator hexadecimal --string-array false && javascript-obfuscator dist/error-handler.js --output dist/error-handler.js --compact true --identifier-names-generator hexadecimal --string-array false", "build:prod": "npm run build && npm run obfuscate", "build:safe": "npm run build && npm run obfuscate:safe", "build:no-obfuscate": "npm run build", "serve": "npx http-server dist -p 8080 -o", "dev": "cross-env NODE_ENV=development npm run generate-config && npx http-server . -p 8080 -o"}, "devDependencies": {"javascript-obfuscator": "^4.1.1", "netlify-cli": "^21.5.0", "copyfiles": "^2.4.1", "rimraf": "^5.0.5", "http-server": "^14.1.1", "cross-env": "^7.0.3", "dotenv": "^16.3.1"}, "keywords": ["pixel-art", "editor", "canvas", "drawing"], "author": "", "license": "MIT"}